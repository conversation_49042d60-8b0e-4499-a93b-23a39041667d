import React from 'react';

// Custom Windows XP style icons
export const FolderIcon = ({ size = 32 }) => (
  <svg width={size} height={size} viewBox="0 0 32 32" fill="none">
    <defs>
      <linearGradient id="folderGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#ffd700" />
        <stop offset="100%" stopColor="#ffb000" />
      </linearGradient>
    </defs>
    <path
      d="M2 6c0-1.1.9-2 2-2h8l2 2h14c1.1 0 2 .9 2 2v18c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6z"
      fill="url(#folderGrad)"
      stroke="#cc8800"
      strokeWidth="1"
    />
    <path
      d="M2 10h28v16c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V10z"
      fill="#ffe066"
    />
  </svg>
);

export const DocumentIcon = ({ size = 32 }) => (
  <svg width={size} height={size} viewBox="0 0 32 32" fill="none">
    <defs>
      <linearGradient id="docGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#ffffff" />
        <stop offset="100%" stopColor="#e0e0e0" />
      </linearGradient>
    </defs>
    <path
      d="M6 2c-1.1 0-2 .9-2 2v24c0 1.1.9 2 2 2h16l6-6V4c0-1.1-.9-2-2-2H6z"
      fill="url(#docGrad)"
      stroke="#999"
      strokeWidth="1"
    />
    <path d="M22 24v4l6-6h-4c-1.1 0-2 .9-2 2z" fill="#ccc" />
    <rect x="8" y="8" width="12" height="1" fill="#666" />
    <rect x="8" y="12" width="12" height="1" fill="#666" />
    <rect x="8" y="16" width="8" height="1" fill="#666" />
  </svg>
);

export const ComputerIcon = ({ size = 32 }) => (
  <svg width={size} height={size} viewBox="0 0 32 32" fill="none">
    <defs>
      <linearGradient id="computerGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#c0c0c0" />
        <stop offset="100%" stopColor="#808080" />
      </linearGradient>
    </defs>
    {/* Monitor */}
    <rect x="4" y="4" width="24" height="18" rx="2" fill="url(#computerGrad)" stroke="#666" />
    <rect x="6" y="6" width="20" height="12" fill="#000080" />
    <rect x="7" y="7" width="18" height="10" fill="#0066cc" />
    {/* Stand */}
    <rect x="14" y="22" width="4" height="4" fill="#999" />
    <rect x="10" y="26" width="12" height="2" fill="#666" />
  </svg>
);

export const MailIcon = ({ size = 32 }) => (
  <svg width={size} height={size} viewBox="0 0 32 32" fill="none">
    <defs>
      <linearGradient id="mailGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#ffffff" />
        <stop offset="100%" stopColor="#e0e0e0" />
      </linearGradient>
    </defs>
    <rect x="2" y="8" width="28" height="18" rx="2" fill="url(#mailGrad)" stroke="#999" />
    <path d="M2 10l14 8 14-8" stroke="#666" strokeWidth="2" fill="none" />
    <path d="M2 8l14 8 14-8" stroke="#333" strokeWidth="1" fill="none" />
  </svg>
);

export const CodeIcon = ({ size = 32 }) => (
  <svg width={size} height={size} viewBox="0 0 32 32" fill="none">
    <defs>
      <linearGradient id="codeGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#4a90e2" />
        <stop offset="100%" stopColor="#2c5aa0" />
      </linearGradient>
    </defs>
    <rect x="2" y="4" width="28" height="24" rx="2" fill="url(#codeGrad)" stroke="#1a4480" />
    <rect x="4" y="6" width="24" height="20" fill="#001122" />
    <text x="6" y="14" fill="#00ff00" fontSize="8" fontFamily="monospace">&lt;/&gt;</text>
    <rect x="6" y="16" width="8" height="1" fill="#00ff00" />
    <rect x="6" y="18" width="12" height="1" fill="#00ff00" />
    <rect x="6" y="20" width="6" height="1" fill="#00ff00" />
  </svg>
);

export const UserIcon = ({ size = 32 }) => (
  <svg width={size} height={size} viewBox="0 0 32 32" fill="none">
    <defs>
      <linearGradient id="userGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#ffd700" />
        <stop offset="100%" stopColor="#cc8800" />
      </linearGradient>
    </defs>
    <circle cx="16" cy="16" r="14" fill="url(#userGrad)" stroke="#996600" strokeWidth="2" />
    <circle cx="16" cy="12" r="5" fill="#fff" />
    <path d="M8 24c0-4 3.5-7 8-7s8 3 8 7" fill="#fff" />
  </svg>
);

export const SkillsIcon = ({ size = 32 }) => (
  <svg width={size} height={size} viewBox="0 0 32 32" fill="none">
    <defs>
      <linearGradient id="skillsGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#ff6b6b" />
        <stop offset="100%" stopColor="#cc4444" />
      </linearGradient>
    </defs>
    <rect x="4" y="4" width="24" height="24" rx="4" fill="url(#skillsGrad)" stroke="#aa3333" strokeWidth="2" />
    <circle cx="12" cy="12" r="2" fill="#fff" />
    <circle cx="20" cy="12" r="2" fill="#fff" />
    <circle cx="16" cy="20" r="2" fill="#fff" />
    <path d="M12 12L16 20L20 12" stroke="#fff" strokeWidth="2" fill="none" />
  </svg>
);

export const ProjectsIcon = ({ size = 32 }) => (
  <svg width={size} height={size} viewBox="0 0 32 32" fill="none">
    <defs>
      <linearGradient id="projectsGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#4ecdc4" />
        <stop offset="100%" stopColor="#26a69a" />
      </linearGradient>
    </defs>
    <rect x="2" y="6" width="28" height="20" rx="2" fill="url(#projectsGrad)" stroke="#1a7a6a" strokeWidth="2" />
    <rect x="4" y="8" width="24" height="16" fill="#fff" />
    <rect x="6" y="10" width="8" height="6" fill="#4ecdc4" />
    <rect x="16" y="10" width="8" height="6" fill="#ff6b6b" />
    <rect x="6" y="18" width="18" height="2" fill="#ccc" />
  </svg>
);
