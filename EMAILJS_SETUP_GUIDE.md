# EmailJS Setup Guide for Contact Form

## 🚀 Quick Setup Steps

### 1. Create EmailJS Account
1. Go to [https://www.emailjs.com/](https://www.emailjs.com/)
2. Click "Sign Up" and create a free account
3. Verify your email address

### 2. Create Email Service
1. In your EmailJS dashboard, go to "Email Services"
2. Click "Add New Service"
3. Choose your email provider (Gmail, Outlook, Yahoo, etc.)
4. Follow the setup instructions for your provider
5. **Copy the Service ID** (you'll need this)

### 3. Create Email Template
1. Go to "Email Templates" in your dashboard
2. Click "Create New Template"
3. Use this template structure:

```
Subject: New Contact Form Message from {{from_name}}

Hello,

You have received a new message from your portfolio contact form:

Name: {{from_name}}
Email: {{from_email}}

Message:
{{message}}

---
This message was sent from your portfolio website.
```

4. **Copy the Template ID** (you'll need this)

### 4. Get Your Public Key
1. Go to "Account" → "General"
2. Find your **Public Key** (User ID)
3. **Copy the Public Key** (you'll need this)

### 5. Update Configuration
Open `src/config/emailjs.js` and replace the placeholder values:

```javascript
export const emailjsConfig = {
  publicKey: 'YOUR_ACTUAL_PUBLIC_KEY',     // From step 4
  serviceId: 'YOUR_ACTUAL_SERVICE_ID',     // From step 2
  templateId: 'YOUR_ACTUAL_TEMPLATE_ID'    // From step 3
};
```

## 🔧 Example Configuration

```javascript
export const emailjsConfig = {
  publicKey: 'user_abc123def456',
  serviceId: 'service_xyz789',
  templateId: 'template_contact_form'
};
```

## 📧 Template Variables

The contact form sends these variables to your email template:
- `{{from_name}}` - User's name
- `{{from_email}}` - User's email
- `{{message}}` - User's message
- `{{to_name}}` - Set to "Portfolio Owner" (you can customize this)

## 🎯 Testing

1. After setup, test your contact form
2. Check your email for received messages
3. Monitor the transmission log for any errors

## 🔒 Security Notes

- Your Public Key is safe to expose in frontend code
- Never expose your Private Key
- EmailJS handles the secure email sending

## 📱 Free Tier Limits

- 200 emails per month
- EmailJS branding in emails
- Upgrade for more features and higher limits

## 🆘 Troubleshooting

### Common Issues:
1. **"User ID not found"** - Check your Public Key
2. **"Service not found"** - Check your Service ID
3. **"Template not found"** - Check your Template ID
4. **Emails not received** - Check spam folder, verify email service setup

### Debug Steps:
1. Open browser console to see error messages
2. Check the transmission log in the contact form
3. Verify all IDs are correct in the config file
4. Test with a simple template first

## 🎉 Success!

Once configured, your contact form will:
- ✅ Send emails directly to your inbox
- ✅ Show real-time transmission status
- ✅ Display cyber-themed success/error messages
- ✅ Clear the form after successful submission
- ✅ Provide detailed logging for debugging
