import React, { useState, useRef, useEffect } from 'react';

const XPWindow = ({
  id,
  title,
  children,
  onClose,
  onMinimize,
  onMaximize,
  isActive = true,
  initialPosition = { x: 100, y: 100 },
  initialSize = { width: 600, height: 400 },
  isMaximized = false,
  isMinimized = false
}) => {
  const [position, setPosition] = useState(initialPosition);
  const [size, setSize] = useState(initialSize);
  const [isDragging, setIsDragging] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [resizeStart, setResizeStart] = useState({ x: 0, y: 0, width: 0, height: 0 });
  
  const windowRef = useRef(null);

  useEffect(() => {
    const handleMouseMove = (e) => {
      if (isDragging) {
        const newX = e.clientX - dragStart.x;
        const newY = e.clientY - dragStart.y;
        setPosition({ 
          x: Math.max(0, Math.min(newX, window.innerWidth - size.width)), 
          y: Math.max(0, Math.min(newY, window.innerHeight - size.height - 40)) 
        });
      } else if (isResizing) {
        const newWidth = Math.max(300, resizeStart.width + (e.clientX - resizeStart.x));
        const newHeight = Math.max(200, resizeStart.height + (e.clientY - resizeStart.y));
        setSize({ width: newWidth, height: newHeight });
      }
    };

    const handleMouseUp = () => {
      setIsDragging(false);
      setIsResizing(false);
      document.body.style.cursor = 'default';
    };

    if (isDragging || isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.userSelect = 'none';
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.style.userSelect = 'auto';
    };
  }, [isDragging, isResizing, dragStart, resizeStart, size]);

  const handleTitleBarMouseDown = (e) => {
    if (e.target.closest('.xp-window-button')) return;
    
    setIsDragging(true);
    const rect = windowRef.current.getBoundingClientRect();
    setDragStart({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    });
  };

  const handleResizeMouseDown = (e) => {
    e.preventDefault();
    setIsResizing(true);
    setResizeStart({
      x: e.clientX,
      y: e.clientY,
      width: size.width,
      height: size.height
    });
    document.body.style.cursor = 'nw-resize';
  };

  const windowStyle = isMaximized 
    ? { 
        position: 'fixed', 
        top: 0, 
        left: 0, 
        width: '100vw', 
        height: 'calc(100vh - 40px)',
        zIndex: isActive ? 100 : 50
      }
    : {
        position: 'fixed',
        left: position.x,
        top: position.y,
        width: size.width,
        height: size.height,
        zIndex: isActive ? 100 : 50
      };

  // Don't render the window if it's minimized
  if (isMinimized) {
    return null;
  }

  return (
    <div
      ref={windowRef}
      className={`xp-window ${isDragging ? 'dragging' : ''}`}
      style={windowStyle}
    >
      {/* Title Bar */}
      <div
        className={`xp-titlebar ${!isActive ? 'inactive' : ''}`}
        onMouseDown={handleTitleBarMouseDown}
      >
        <div className="xp-titlebar-text">{title}</div>
        <div className="flex">
          {/* Minimize Button */}
          <button
            className="xp-window-button"
            onClick={onMinimize}
            title="Minimize"
          >
            <span style={{ fontSize: '8px' }}>_</span>
          </button>
          
          {/* Maximize/Restore Button */}
          <button
            className="xp-window-button"
            onClick={onMaximize}
            title={isMaximized ? "Restore" : "Maximize"}
          >
            <span style={{ fontSize: '8px' }}>□</span>
          </button>
          
          {/* Close Button */}
          <button
            className="xp-window-button"
            onClick={onClose}
            title="Close"
            style={{ color: '#000' }}
          >
            <span style={{ fontSize: '8px' }}>×</span>
          </button>
        </div>
      </div>

      {/* Window Content */}
      <div className="window-content" style={{ height: 'calc(100% - 30px)' }}>
        {children}
      </div>

      {/* Resize Handle */}
      {!isMaximized && (
        <div
          className="absolute bottom-0 right-0 w-4 h-4 cursor-nw-resize"
          onMouseDown={handleResizeMouseDown}
          style={{
            background: 'linear-gradient(-45deg, transparent 0%, transparent 30%, #999 30%, #999 40%, transparent 40%, transparent 60%, #999 60%, #999 70%, transparent 70%)'
          }}
        />
      )}
    </div>
  );
};

export default XPWindow;
