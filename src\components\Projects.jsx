import React from 'react';
import { ExternalLink, Monitor } from 'lucide-react';
import { images } from '../constants';


const Projects = () => {
  const projects = [
    {
      name: "Array-Vitrine",
      status: "STABLE",
      description: "Digital Agency Website",
      tech: "Html, Css",
      image: images.array,
      demoUrl: "#",
      githubUrl: "#",
      category: "WEBSITE"
    },
    {
      name: "Doob-Vitrine",
      status: "ACTIVE",
      description: "Modern Business Website",
      tech: "Html, Css",
      image: images.doob,
      demoUrl: "#",
      githubUrl: "#",
      category: "WEBSITE"
    },
    {
      name: "Sneakers E-commerce",
      status: "STABLE",
      description: "E-commerce landing page for sneakers",
      tech: "Html, Css, Sass",
      image: images.sneakers,
      demoUrl: "#",
      githubUrl: "#",
      category: "E-COMMERCE"
    },
    {
      name: "EasyFolio",
      status: "ACTIVE",
      description: "Portfolio website template",
      tech: "Html, Css, JavaScript",
      image: images.easyfolio,
      demoUrl: "#",
      githubUrl: "#",
      category: "PORTFOLIO"
    },
    {
      name: "Quickstart",
      status: "STABLE",
      description: "Quick startup landing page",
      tech: "Html, Css, Bootstrap",
      image: images.quickstart,
      demoUrl: "#",
      githubUrl: "#",
      category: "LANDING"
    },
    {
      name: "Restaurantly",
      status: "ACTIVE",
      description: "Restaurant website with menu and reservations",
      tech: "Html, Sass, Bootstrap, JavaScript",
      image: images.restaurantly,
      demoUrl: "#",
      githubUrl: "#",
      category: "RESTAURANT"
    }
  ];

  return (
    <div className="space-y-6">
      <h2 className="text-white text-xl font-mono mb-6">Projects - Installed Software</h2>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {projects.map((project, index) => (
          <div
            key={index}
            className="border border-gray-700 bg-black/50 hover:bg-black/70 transition-all duration-300 group overflow-hidden"
          >
            <div className="relative h-48 overflow-hidden">
              <img
                src={project.image}
                alt={project.name}
                className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                style={{
                  objectPosition: "center top"
                }}
              />
              <div className="absolute inset-0 bg-black/40 group-hover:bg-black/20 transition-all duration-300" />

              <div className="absolute top-3 left-3 bg-gray-900/90 border border-gray-600 px-2 py-1 text-xs text-gray-300 font-mono">
                {project.category}
              </div>

              <div className="absolute top-3 right-3 bg-gray-800/90 border border-gray-600 px-2 py-1 text-xs text-gray-300 font-mono">
                {project.status}
              </div>

              <div className="absolute bottom-3 right-3 flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <button
                  onClick={() => window.open(project.demoUrl, '_blank')}
                  className="bg-blue-600/80 hover:bg-blue-600 border border-blue-500 p-2 text-white transition-colors duration-200"
                  title="View Demo"
                >
                  <ExternalLink size={16} />
                </button>
                <button
                  onClick={() => window.open(project.githubUrl, '_blank')}
                  className="bg-gray-700/80 hover:bg-gray-700 border border-gray-600 p-2 text-white transition-colors duration-200"
                  title="View Code"
                >
                  <Monitor size={16} />
                </button>
              </div>
            </div>

            <div className="p-4">
              <div className="text-white font-mono font-bold mb-2 text-lg">
                {project.name}
              </div>
              <div className="text-gray-300 text-sm font-mono mb-3 leading-relaxed">
                {project.description}
              </div>
              <div className="text-xs text-gray-500 font-mono border-t border-gray-700 pt-2">
                Tech Stack: {project.tech}
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="border border-gray-700 bg-black/30 p-3 text-center">
        <div className="text-gray-400 font-mono text-sm">
          <span className="animate-pulse">█</span> {projects.length} APPLICATIONS INSTALLED <span className="animate-pulse">█</span>
        </div>
        <div className="text-xs text-gray-500 font-mono mt-1">
          STATUS: ALL_SYSTEMS_OPERATIONAL • LAST_UPDATE: {new Date().toLocaleDateString()}
        </div>
      </div>
    </div>
  );
};

export default Projects;