import React, { useState, useEffect } from 'react';

const Resume = () => {
  const [currentTime, setCurrentTime] = useState('');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const updateTime = () => {
      const now = new Date();
      const timeString = now.toTimeString().slice(0, 8);
      setCurrentTime(timeString);
    };

    updateTime();
    const interval = setInterval(updateTime, 1000);

    setTimeout(() => setIsLoading(false), 2000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="relative min-h-screen bg-black overflow-hidden">
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0" style={{
          backgroundImage: `
            linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px)
          `,
          backgroundSize: '30px 30px',
          animation: 'grid-move 20s linear infinite'
        }}></div>
      </div>

      <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-96 h-32 bg-cyan-400/20 rounded-full blur-3xl animate-pulse"></div>

      <div className="relative z-10 p-3 md:p-6 space-y-4 md:space-y-8">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4 md:mb-8 space-y-2 md:space-y-0">
          <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
            <div className="bg-cyan-400/20 border border-cyan-400 px-2 md:px-4 py-1 md:py-2 font-mono text-cyan-400 text-xs md:text-sm">
              {currentTime} {'>'} {'>'} {'>'} {'>'} {'>'}
            </div>
            <div className="text-cyan-400 font-mono text-xs animate-pulse">
              SYSTEM_STATUS: ONLINE
            </div>
          </div>
          <div className="text-cyan-400 font-mono text-xs">
            BIOS_VERSION: 2.1.4
          </div>
        </div>

        <div className="relative">
          <div className="relative bg-gradient-to-br from-cyan-900/20 to-blue-900/20 border-2 border-cyan-400/50 p-4 md:p-8"
               style={{
                 clipPath: 'polygon(0 0, calc(100% - 20px) 0, 100% 20px, 100% 100%, 20px 100%, 0 calc(100% - 20px))',
                 boxShadow: '0 0 20px rgba(0, 255, 255, 0.3), inset 0 0 20px rgba(0, 255, 255, 0.1)'
               }}>

            <div className="absolute top-2 left-2 w-6 h-6 border-l-2 border-t-2 border-cyan-400"></div>
            <div className="absolute top-2 right-2 w-6 h-6 border-r-2 border-t-2 border-cyan-400"></div>
            <div className="absolute bottom-2 left-2 w-6 h-6 border-l-2 border-b-2 border-cyan-400"></div>
            <div className="absolute bottom-2 right-2 w-6 h-6 border-r-2 border-b-2 border-cyan-400"></div>
            {isLoading && (
              <div className="absolute inset-0 bg-black/80 flex items-center justify-center z-20">
                <div className="text-cyan-400 font-mono text-center">
                  <div className="text-lg mb-4">LOADING PERSONNEL DATA...</div>
                  <div className="flex space-x-1">
                    {[...Array(10)].map((_, i) => (
                      <div key={i} className="w-2 h-2 bg-cyan-400 animate-pulse"
                           style={{ animationDelay: `${i * 0.1}s` }}></div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 md:gap-8">
              <div className="space-y-4 md:space-y-6">
                <div className="bg-black/40 border border-cyan-400/30 p-4 md:p-6">
                  <div className="text-cyan-400 font-mono text-sm mb-3 border-b border-cyan-400/30 pb-2">
                    [PERSONAL_DATA]
                  </div>
                  <div className="space-y-2 text-gray-300 font-mono text-xs">
                    <div className="flex justify-between">
                      <span className="text-cyan-400">NAME:</span>
                      <span>CLASSIFIED</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-cyan-400">STATUS:</span>
                      <span className="text-green-400">ACTIVE</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-cyan-400">CLEARANCE:</span>
                      <span className="text-yellow-400">LEVEL_7</span>
                    </div>
                  </div>
                </div>

                <div className="bg-black/40 border border-cyan-400/30 p-4">
                  <div className="text-cyan-400 font-mono text-sm mb-3 border-b border-cyan-400/30 pb-2">
                    [SKILL_MATRIX]
                  </div>
                  <div className="space-y-2">
                    {[
                      'HTML/CSS',
                      'JavaScript',
                      'React',
                      'Python',
                      'Sass',
                      'Bootstrap'
                    ].map((skill, index) => (
                      <div key={index} className="flex items-center space-x-3">
                        <div className="w-2 h-2 bg-cyan-400 animate-pulse"></div>
                        <div className="text-gray-300 font-mono text-xs">{skill}</div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              <div className="lg:col-span-2 space-y-4 md:space-y-6">
                <div className="bg-black/40 border border-cyan-400/30 p-4 md:p-6">
                  <div className="text-cyan-400 font-mono text-lg mb-4 border-b border-cyan-400/30 pb-2">
                    [EXPERIENCE_LOG]
                  </div>
                  <div className="space-y-4">
                    <div className="border-l-2 border-cyan-400/50 pl-4">
                      <div className="text-green-400 font-mono text-sm">2023 - 2024</div>
                      <div className="text-white font-mono text-base">Information Technology Support</div>
                      <div className="text-gray-400 font-mono text-xs mt-1">
                        • System maintenance and troubleshooting<br/>
                        • User support and technical assistance<br/>
                        • Network configuration and monitoring
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-black/40 border border-cyan-400/30 p-4 md:p-6">
                  <div className="text-cyan-400 font-mono text-sm md:text-lg mb-3 md:mb-4 border-b border-cyan-400/30 pb-2">
                    [EDUCATION_RECORDS]
                  </div>
                  <div className="space-y-4">
                    <div className="border-l-2 border-blue-400/50 pl-4">
                      <div className="text-blue-400 font-mono text-sm">2021 - 2023</div>
                      <div className="text-white font-mono text-base">Physics Degree - University</div>
                      <div className="text-gray-400 font-mono text-xs mt-1">
                        Advanced theoretical and practical physics studies
                      </div>
                    </div>
                    <div className="border-l-2 border-blue-400/50 pl-4">
                      <div className="text-blue-400 font-mono text-sm">2021</div>
                      <div className="text-white font-mono text-base">High School - Physics Specialization</div>
                      <div className="text-gray-400 font-mono text-xs mt-1">
                        Foundation in scientific methodology and analysis
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-black/40 border border-cyan-400/30 p-4 md:p-6">
                  <div className="text-cyan-400 font-mono text-sm md:text-lg mb-3 md:mb-4 border-b border-cyan-400/30 pb-2">
                    [CERTIFICATIONS]
                  </div>
                  <div className="space-y-3">
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-green-400 animate-pulse"></div>
                      <div className="text-white font-mono text-sm">AI Career Certification - ALX</div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-green-400 animate-pulse"></div>
                      <div className="text-white font-mono text-sm">Professional Foundation - ALX</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-6 md:mt-8 pt-3 md:pt-4 border-t border-cyan-400/30 flex flex-col md:flex-row md:justify-between md:items-center space-y-2 md:space-y-0">
              <div className="text-cyan-400 font-mono text-xs">
                LAST_UPDATE: {new Date().toLocaleDateString()}
              </div>
              <div className="flex flex-wrap gap-2 md:space-x-4 text-xs font-mono">
                <span className="text-green-400">● SYSTEMS_OPERATIONAL</span>
                <span className="text-yellow-400">● DATA_VERIFIED</span>
                <span className="text-cyan-400">● READY_FOR_DEPLOYMENT</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        @keyframes grid-move {
          0% { transform: translate(0, 0); }
          100% { transform: translate(30px, 30px); }
        }
      `}</style>
    </div>
  );
};

export default Resume;