# 🔮 Cyber Logo Source Code

## 📁 File Location
```
src/components/CyberLogo.jsx
```

## 🎯 Complete Source Code

```jsx
import React from 'react';

const CyberLogo = ({ size = 'default', showStatus = true }) => {
  const sizeClasses = {
    small: 'text-lg',
    default: 'text-2xl',
    large: 'text-3xl',
    xl: 'text-4xl'
  };

  const dotSizes = {
    small: 'w-1 h-1',
    default: 'w-2 h-2',
    large: 'w-3 h-3',
    xl: 'w-4 h-4'
  };

  return (
    <div className="relative flex items-center">
      {/* Main Logo */}
      <div className="relative">
        {/* Primary Logo Text */}
        <div className={`${sizeClasses[size]} font-bold text-cyan-400 font-mono tracking-wider relative z-10`}>
          <span className="relative">
            H
            {/* Animated dot on H */}
            <div className={`absolute -top-1 -right-1 ${dotSizes[size]} bg-cyan-400 animate-pulse`}></div>
          </span>
          
          {/* Diamond separator */}
          <span className="text-blue-400 mx-1">◊</span>
          
          <span className="relative">
            M
            {/* Animated dot on M */}
            <div 
              className={`absolute -bottom-1 -left-1 ${dotSizes[size]} bg-blue-400 animate-pulse`} 
              style={{ animationDelay: '0.5s' }}
            ></div>
          </span>
        </div>
        
        {/* Shadow/Depth Layer */}
        <div className={`absolute inset-0 ${sizeClasses[size]} font-bold text-cyan-400/20 font-mono tracking-wider transform translate-x-0.5 translate-y-0.5`}>
          H◊M
        </div>
        
        {/* Geometric Border Animations */}
        <div className="absolute -inset-2 border border-cyan-400/30 transform rotate-45 animate-pulse"></div>
        <div 
          className="absolute -inset-1 border border-blue-400/20 transform -rotate-45 animate-pulse" 
          style={{ animationDelay: '1s' }}
        ></div>
      </div>
      
      {/* Status Indicators */}
      {showStatus && (
        <div className="ml-3 flex flex-col">
          {/* Status Lights */}
          <div className="flex items-center space-x-1">
            <div className="w-1 h-1 bg-green-400 animate-pulse"></div>
            <div 
              className="w-1 h-1 bg-yellow-400 animate-pulse" 
              style={{ animationDelay: '0.3s' }}
            ></div>
            <div 
              className="w-1 h-1 bg-red-400 animate-pulse" 
              style={{ animationDelay: '0.6s' }}
            ></div>
          </div>
          
          {/* Status Text */}
          <div className="text-xs text-cyan-400 font-mono mt-1">
            SYS_ONLINE
          </div>
        </div>
      )}
    </div>
  );
};

export default CyberLogo;
```

## 🚀 Usage Examples

### Basic Usage
```jsx
import CyberLogo from './components/CyberLogo';

// Default size with status indicators
<CyberLogo />
```

### Different Sizes
```jsx
// Small logo
<CyberLogo size="small" />

// Large logo
<CyberLogo size="large" />

// Extra large logo
<CyberLogo size="xl" />
```

### Without Status Indicators
```jsx
// Logo only, no status lights
<CyberLogo showStatus={false} />
```

### Combined Options
```jsx
// Large logo without status
<CyberLogo size="large" showStatus={false} />
```

## 🎨 Design Elements

### Colors Used
- **Primary**: `text-cyan-400` (#00FFFF)
- **Secondary**: `text-blue-400` (#60A5FA)
- **Shadow**: `text-cyan-400/20` (20% opacity)
- **Status Green**: `bg-green-400` (#4ADE80)
- **Status Yellow**: `bg-yellow-400` (#FACC15)
- **Status Red**: `bg-red-400` (#F87171)

### Typography
- **Font**: `font-mono` (monospace)
- **Weight**: `font-bold`
- **Spacing**: `tracking-wider`

### Animations
- **Pulse**: `animate-pulse` on all dots and borders
- **Delays**: 0.3s, 0.5s, 0.6s, 1s for staggered effects

## 🔧 Customization

### Adding New Sizes
```jsx
const sizeClasses = {
  small: 'text-lg',
  default: 'text-2xl',
  large: 'text-3xl',
  xl: 'text-4xl',
  xxl: 'text-5xl'  // Add new size
};

const dotSizes = {
  small: 'w-1 h-1',
  default: 'w-2 h-2',
  large: 'w-3 h-3',
  xl: 'w-4 h-4',
  xxl: 'w-5 h-5'  // Add corresponding dot size
};
```

### Changing Colors
```jsx
// Replace color classes
text-cyan-400    → text-purple-400
text-blue-400    → text-pink-400
bg-cyan-400      → bg-purple-400
bg-blue-400      → bg-pink-400
```

### Custom Status Text
```jsx
<div className="text-xs text-cyan-400 font-mono mt-1">
  CUSTOM_STATUS  // Change this text
</div>
```

## 📱 Responsive Behavior
- Scales automatically with size prop
- Maintains aspect ratio on all devices
- Optimized animations for performance

## 🎯 Props Reference

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `size` | string | 'default' | Logo size: 'small', 'default', 'large', 'xl' |
| `showStatus` | boolean | true | Show/hide status indicators |

## 🔮 Logo Structure
```
H ◊ M          ← Main logo text
│ │ │
│ │ └── M with animated dot (bottom-left)
│ └──── Diamond separator (◊)
└────── H with animated dot (top-right)

[●●●] SYS_ONLINE  ← Status indicators
 │││
 ││└── Red status light (0.6s delay)
 │└─── Yellow status light (0.3s delay)
 └──── Green status light

Geometric borders:
- Outer: 45° rotation with pulse
- Inner: -45° rotation with 1s delay
```
