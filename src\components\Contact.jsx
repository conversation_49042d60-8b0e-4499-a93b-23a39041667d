import React, { useState, useEffect } from 'react';
import emailjs from '@emailjs/browser';
import { emailjsConfig } from '../config/emailjs';

const Contact = () => {
  const [currentTime, setCurrentTime] = useState('');
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState('STANDBY');
  const [transmissionLog, setTransmissionLog] = useState([
    '> System initialized...',
    '> Secure channel established',
    '> Awaiting transmission data'
  ]);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  });

  useEffect(() => {
    const updateTime = () => {
      const now = new Date();
      const timeString = now.toTimeString().slice(0, 8);
      setCurrentTime(timeString);
    };

    updateTime();
    const interval = setInterval(updateTime, 1000);

    try {
      emailjs.init(emailjsConfig.publicKey);
      addToLog('EmailJS initialized successfully');
    } catch (error) {
      console.error('EmailJS initialization error:', error);
      addToLog('ERROR: Failed to initialize email service');
    }

    return () => clearInterval(interval);
  }, []);

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const addToLog = (message) => {
    setTransmissionLog(prev => [...prev, `> ${message}`]);
  };

  const handleTransmit = async () => {
    if (!formData.name || !formData.email || !formData.message) {
      addToLog('ERROR: Missing required transmission data');
      setConnectionStatus('TRANSMISSION_FAILED');
      setTimeout(() => setConnectionStatus('STANDBY'), 3000);
      return;
    }

    setIsConnecting(true);
    setConnectionStatus('ESTABLISHING_CONNECTION');
    addToLog('Initializing secure transmission...');
    addToLog(`Using Service ID: ${emailjsConfig.serviceId}`);
    addToLog(`Using Template ID: ${emailjsConfig.templateId}`);

    try {
      const templateParams = {
        from_name: formData.name,
        from_email: formData.email,
        message: formData.message,
        to_name: 'Portfolio Owner',
      };

      addToLog('Template parameters prepared');

      setTimeout(async () => {
        setConnectionStatus('TRANSMITTING_DATA');
        addToLog('Encrypting data payload...');

        try {
          const response = await emailjs.send(
            emailjsConfig.serviceId,
            emailjsConfig.templateId,
            templateParams
          );

          console.log('EmailJS Response:', response);
          addToLog(`EmailJS Response Status: ${response.status}`);
          addToLog(`EmailJS Response Text: ${response.text}`);

          if (response.status === 200) {
            setConnectionStatus('MESSAGE_SENT');
            addToLog('Transmission successful - Message delivered');
            addToLog(`Response code: ${response.status}`);
            setIsConnecting(false);
            setFormData({ name: '', email: '', message: '' });

            setTimeout(() => {
              setConnectionStatus('STANDBY');
              addToLog('System ready for next transmission');
            }, 2000);
          } else {
            throw new Error(`Unexpected response status: ${response.status}`);
          }
        } catch (innerError) {
          console.error('Inner EmailJS Error:', innerError);
          setConnectionStatus('TRANSMISSION_FAILED');
          addToLog(`ERROR: ${innerError.text || innerError.message || 'Transmission failed'}`);
          addToLog('Please check your EmailJS configuration');
          setIsConnecting(false);

          setTimeout(() => {
            setConnectionStatus('STANDBY');
          }, 3000);
        }
      }, 1000);

    } catch (error) {
      console.error('Outer EmailJS Error:', error);
      setConnectionStatus('TRANSMISSION_FAILED');
      addToLog(`ERROR: ${error.text || error.message || 'Transmission failed'}`);
      addToLog('Please check network connection and try again');
      setIsConnecting(false);

      setTimeout(() => {
        setConnectionStatus('STANDBY');
      }, 3000);
    }
  };

  return (
    <div className="relative min-h-screen bg-black overflow-hidden">
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `
            linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px)
          `,
          backgroundSize: '40px 40px',
          animation: 'grid-move 25s linear infinite'
        }}></div>
      </div>

      <div className="absolute top-1/4 left-1/4 w-32 h-32 md:w-64 md:h-64 bg-cyan-400/10 rounded-full blur-3xl animate-pulse"></div>
      <div className="absolute bottom-1/4 right-1/4 w-48 h-16 md:w-96 md:h-32 bg-blue-400/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }}></div>

      <div className="relative z-10 p-3 md:p-6 space-y-4 md:space-y-8">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4 md:mb-8 space-y-2 md:space-y-0">
          <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
            <div className="bg-cyan-400/20 border border-cyan-400 px-2 md:px-4 py-1 md:py-2 font-mono text-cyan-400 text-xs md:text-sm">
              {currentTime} {'>'} COMM_LINK {'>'} ACTIVE
            </div>
            <div className="text-cyan-400 font-mono text-xs animate-pulse">
              NETWORK_STATUS: {connectionStatus}
            </div>
          </div>
          <div className="text-cyan-400 font-mono text-xs">
            PROTOCOL: SECURE_CHANNEL_v3.2
          </div>
        </div>

        <div className="relative">
          <div className="relative bg-gradient-to-br from-cyan-900/20 to-blue-900/20 border-2 border-cyan-400/50 p-4 md:p-8"
            style={{
              clipPath: 'polygon(0 0, calc(100% - 20px) 0, 100% 20px, 100% 100%, 20px 100%, 0 calc(100% - 20px))',
              boxShadow: '0 0 20px rgba(0, 255, 255, 0.3), inset 0 0 20px rgba(0, 255, 255, 0.1)'
            }}>

            <div className="absolute top-2 left-2 w-6 h-6 border-l-2 border-t-2 border-cyan-400"></div>
            <div className="absolute top-2 right-2 w-6 h-6 border-r-2 border-t-2 border-cyan-400"></div>
            <div className="absolute bottom-2 left-2 w-6 h-6 border-l-2 border-b-2 border-cyan-400"></div>
            <div className="absolute bottom-2 right-2 w-6 h-6 border-r-2 border-b-2 border-cyan-400"></div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-8">
              <div className="space-y-4 md:space-y-6">
                <div className="bg-black/40 border border-cyan-400/30 p-4 md:p-6">
                  <div className="text-cyan-400 font-mono text-sm md:text-lg mb-3 md:mb-4 border-b border-cyan-400/30 pb-2">
                    [NETWORK_ENDPOINTS]
                  </div>
                  <div className="space-y-3 md:space-y-4">
                    <div className="flex items-start space-x-3 group">
                      <div className="w-3 h-3 bg-green-400 animate-pulse mt-1"></div>
                      <div className="text-gray-300 font-mono text-xs md:text-sm">
                        <span className="text-cyan-400">EMAIL_ADDR:</span>
                        <br />
                        <span className="text-white group-hover:text-cyan-400 transition-colors cursor-pointer break-all">
                          <EMAIL>
                        </span>
                      </div>
                    </div>

                    <div className="flex items-start space-x-3 group">
                      <div className="w-3 h-3 bg-green-400 animate-pulse mt-1" style={{ animationDelay: '0.5s' }}></div>
                      <div className="text-gray-300 font-mono text-xs md:text-sm">
                        <span className="text-cyan-400">GITHUB_REPO:</span>
                        <br />
                        <span className="text-white group-hover:text-cyan-400 transition-colors cursor-pointer break-all">
                          github.com/07asfah
                        </span>
                      </div>
                    </div>

                    <div className="flex items-start space-x-3 group">
                      <div className="w-3 h-3 bg-green-400 animate-pulse mt-1" style={{ animationDelay: '1s' }}></div>
                      <div className="text-gray-300 font-mono text-xs md:text-sm">
                        <span className="text-cyan-400">LOCATION:</span>
                        <br />
                        <span className="text-white group-hover:text-cyan-400 transition-colors">
                          Morocco, North Africa
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-black/40 border border-cyan-400/30 p-4">
                  <div className="text-cyan-400 font-mono text-sm mb-3 border-b border-cyan-400/30 pb-2">
                    [CONNECTION_STATUS]
                  </div>
                  <div className="space-y-2 text-xs font-mono">
                    <div className="flex justify-between">
                      <span className="text-gray-300">ENCRYPTION:</span>
                      <span className="text-green-400">AES-256</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-300">LATENCY:</span>
                      <span className="text-yellow-400">12ms</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-300">BANDWIDTH:</span>
                      <span className="text-cyan-400">1.2 Gbps</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-300">UPTIME:</span>
                      <span className="text-green-400">99.9%</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-4 md:space-y-6">
                <div className="bg-black/40 border border-cyan-400/30 p-4 md:p-6">
                  <div className="text-cyan-400 font-mono text-sm md:text-lg mb-3 md:mb-4 border-b border-cyan-400/30 pb-2">
                    [MESSAGE_TRANSMISSION]
                  </div>

                  <div className="space-y-3 md:space-y-4">
                    <div>
                      <label className="text-cyan-400 font-mono text-xs mb-2 block">SENDER_ID:</label>
                      <input
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        placeholder="Enter identification..."
                        className="w-full bg-black/60 border border-cyan-400/30 text-gray-300 px-3 py-2 font-mono text-xs md:text-sm focus:outline-none focus:border-cyan-400 focus:shadow-[0_0_10px_rgba(0,255,255,0.3)] transition-all"
                      />
                    </div>

                    <div>
                      <label className="text-cyan-400 font-mono text-xs mb-2 block">RETURN_ADDRESS:</label>
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        placeholder="Enter return channel..."
                        className="w-full bg-black/60 border border-cyan-400/30 text-gray-300 px-3 py-2 font-mono text-xs md:text-sm focus:outline-none focus:border-cyan-400 focus:shadow-[0_0_10px_rgba(0,255,255,0.3)] transition-all"
                      />
                    </div>

                    <div>
                      <label className="text-cyan-400 font-mono text-xs mb-2 block">DATA_PAYLOAD:</label>
                      <textarea
                        rows="3"
                        name="message"
                        value={formData.message}
                        onChange={handleInputChange}
                        placeholder="Enter transmission data..."
                        className="w-full bg-black/60 border border-cyan-400/30 text-gray-300 px-3 py-2 font-mono text-xs md:text-sm focus:outline-none focus:border-cyan-400 focus:shadow-[0_0_10px_rgba(0,255,255,0.3)] transition-all resize-none"
                      ></textarea>
                    </div>

                    <button
                      onClick={handleTransmit}
                      disabled={isConnecting}
                      className={`w-full border-2 py-2 md:py-3 font-mono text-xs md:text-sm transition-all duration-300 ${isConnecting
                          ? 'border-yellow-400 text-yellow-400 bg-yellow-400/10 animate-pulse'
                          : 'border-cyan-400 text-cyan-400 hover:bg-cyan-400/20 hover:shadow-[0_0_20px_rgba(0,255,255,0.5)]'
                        }`}
                    >
                      {isConnecting ? 'TRANSMITTING...' : 'INITIATE_TRANSMISSION'}
                    </button>
                  </div>
                </div>

                <div className="bg-black/40 border border-cyan-400/30 p-4">
                  <div className="text-cyan-400 font-mono text-sm mb-3 border-b border-cyan-400/30 pb-2">
                    [TRANSMISSION_LOG]
                  </div>
                  <div className="space-y-1 text-xs font-mono text-gray-400 h-20 md:h-24 overflow-y-auto">
                    {transmissionLog.map((log, index) => (
                      <div key={index} className={log.includes('ERROR') ? 'text-red-400' : 'text-gray-400'}>
                        {log}
                      </div>
                    ))}
                    {connectionStatus !== 'STANDBY' && (
                      <div className="text-cyan-400 animate-pulse">
                        {'>'} {connectionStatus.replace('_', ' ').toLowerCase()}...
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-6 md:mt-8 pt-3 md:pt-4 border-t border-cyan-400/30 flex flex-col md:flex-row md:justify-between md:items-center space-y-2 md:space-y-0">
              <div className="text-cyan-400 font-mono text-xs">
                LAST_PING: {new Date().toLocaleTimeString()}
              </div>
              <div className="flex flex-wrap gap-2 md:space-x-4 text-xs font-mono">
                <span className="text-green-400">● SECURE_CONNECTION</span>
                <span className="text-cyan-400">● READY_TO_TRANSMIT</span>
                <span className="text-blue-400">● MONITORING_ACTIVE</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        @keyframes grid-move {
          0% { transform: translate(0, 0); }
          100% { transform: translate(40px, 40px); }
        }
      `}</style>
    </div>
  );
};

export default Contact;