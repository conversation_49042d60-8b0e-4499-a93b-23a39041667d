import React, { useState, useEffect } from 'react';
import { DiJavascript } from 'react-icons/di';
import { FaBootstrap, FaCss3Alt, FaGitAlt, FaGithub, FaHtml5, FaSass } from 'react-icons/fa';
import { RiReactjsFill, RiTailwindCssFill } from 'react-icons/ri';

const FloatingSkill = ({ skill, index, isActive, onClick, containerBounds, allPositions, updatePosition }) => {
  const [position, setPosition] = useState(() => {
    const gridX = (index % 3) * (containerBounds.width / 3) + Math.random() * (containerBounds.width / 6);
    const gridY = Math.floor(index / 3) * (containerBounds.height / 2) + Math.random() * (containerBounds.height / 4);
    return {
      x: Math.min(gridX, containerBounds.width - 100),
      y: Math.min(gridY, containerBounds.height - 100)
    };
  });
  const [velocity, setVelocity] = useState({
    x: (Math.random() > 0.5 ? 1 : -1) * (5 + Math.random() * 4),
    y: (Math.random() > 0.5 ? 1 : -1) * (5 + Math.random() * 4)
  });


  useEffect(() => {
    const moveInterval = setInterval(() => {
      setPosition(prev => {
        let newX = prev.x + velocity.x;
        let newY = prev.y + velocity.y;
        let newVelX = velocity.x;
        let newVelY = velocity.y;
        if (allPositions) {
          allPositions.forEach((otherPos, otherIndex) => {
            if (otherIndex !== index && otherPos) {
              const dx = newX - otherPos.x;
              const dy = newY - otherPos.y;
              const distance = Math.sqrt(dx * dx + dy * dy);
              const minDistance = 100;

              if (distance < minDistance && distance > 0) {
                const repelForce = (minDistance - distance) / minDistance;
                const repelX = (dx / distance) * repelForce * 3;
                const repelY = (dy / distance) * repelForce * 3;

                newX += repelX;
                newY += repelY;


                newVelX += repelX * 0.1;
                newVelY += repelY * 0.1;
              }
            }
          });
        }

        // DVD-style perfect edge bouncing
        if (newX <= 0) {
          newVelX = Math.abs(newVelX);
          newX = 0;
        } else if (newX >= containerBounds.width - 70) { // Adjusted for bigger icons
          newVelX = -Math.abs(newVelX);
          newX = containerBounds.width - 70;
        }

        if (newY <= 0) {
          newVelY = Math.abs(newVelY);
          newY = 0;
        } else if (newY >= containerBounds.height - 70) { // Adjusted for bigger icons
          newVelY = -Math.abs(newVelY);
          newY = containerBounds.height - 70;
        }

        setVelocity({ x: newVelX, y: newVelY });

        const newPosition = { x: newX, y: newY };

        if (updatePosition) {
          updatePosition(index, newPosition);
        }

        return newPosition;
      });
    }, 16); // ~60 FPS for smooth DVD-like movement

    return () => {
      clearInterval(moveInterval);
    };
  }, [velocity, containerBounds]);

  const getIcon = (name) => {
    const icons = {
      'JavaScript': () => (
      <DiJavascript size={60} color='yellow' />
      ),
      'React.js': () => (
      <RiReactjsFill size={60} color='#61DBFB' />
      ),
      'HTML': () => (
        <FaHtml5 size={60} color='#f06529' />
      ),
      'CSS': () => (
        <FaCss3Alt size={60} color='#2965f1' />
      ),
      'Git': () => (
      <FaGitAlt size={60} color='#F1502F' />
      ),
      'Github': () => (
      <FaGithub size={60} color='white' />
      ),
      'TailwindCss': () => (
      <RiTailwindCssFill size={60} color='#2965f1' />
      ),
      'Bootstrap': () => (
        <FaBootstrap size={60} color='purple' />
      )
    };
    return icons[name] || icons['JavaScript'];
  };

  const Icon = getIcon(skill.name);

  return (
    <div
      className={`absolute cursor-pointer transition-all duration-300 ${
        isActive ? 'z-20' : 'hover:z-10'
      }`}
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
        transform: isActive ? 'scale(1.2)' : 'scale(1)',
      }}
      onClick={() => onClick(skill)}
    >
      <div
        className="transition-all duration-300 relative"
        style={{
          transform: isActive ? 'scale(1.2)' : 'scale(1)'
        }}
      >
        <Icon />
      </div>


    </div>
  );
};

const Skills = () => {
  const [selectedSkill, setSelectedSkill] = useState(null);
  const [containerBounds, setContainerBounds] = useState({ width: 800, height: 450 });
  const [allPositions, setAllPositions] = useState(new Array(18).fill(null)); // 2 instances of 9 skills

  const updatePosition = (index, position) => {
    setAllPositions(prev => {
      const newPositions = [...prev];
      newPositions[index] = position;
      return newPositions;
    });
  };

  useEffect(() => {
    const updateBounds = () => {
      const container = document.getElementById('skills-container');
      if (container) {
        const rect = container.getBoundingClientRect();
        setContainerBounds({ width: rect.width, height: rect.height });
      }
    };

    updateBounds();
    window.addEventListener('resize', updateBounds);
    return () => window.removeEventListener('resize', updateBounds);
  }, []);

  const coreSkills = [
    {
      name: 'JavaScript',
      level: 4,
      lightColor: '#F5F5F5',
      darkColor: '#888888',
      description: 'Modern ES6+ JavaScript, DOM manipulation, async programming'
    },
    {
      name: 'React.js',
      level: 4,
      lightColor: '#FFFFFF',
      darkColor: '#666666',
      description: 'Component-based architecture, hooks, state management'
    },
    {
      name: 'HTML',
      level: 5,
      lightColor: '#E8E8E8',
      darkColor: '#444444',
      description: 'Semantic markup, accessibility, modern HTML5 features'
    },
    {
      name: 'CSS',
      level: 4,
      lightColor: '#F0F0F0',
      darkColor: '#555555',
      description: 'Responsive design, animations, modern CSS Grid & Flexbox'
    },
    {
      name: 'SASS',
      level: 3,
      lightColor: '#DDDDDD',
      darkColor: '#777777',
      description: 'CSS preprocessing, mixins, variables, modular stylesheets'
    },
    {
      name: 'Bootstrap',
      level: 2,
      lightColor: '#F8F8F8',
      darkColor: '#999999',
      description: 'Responsive framework, component library, utility classes'
    },
    {
      name: 'Git',
      level: 2,
      lightColor: '#F8F8F8',
      darkColor: '#999999',
      description: 'Responsive framework, component library, utility classes'
    },
    {
      name: 'Github',
      level: 2,
      lightColor: '#F8F8F8',
      darkColor: '#999999',
      description: 'Responsive framework, component library, utility classes'
    },
    {
      name: 'TailwindCss',
      level: 2,
      lightColor: '#F8F8F8',
      darkColor: '#999999',
      description: 'Responsive framework, component library, utility classes'
    }
  ];

  return (
    <div className="space-y-6">
      <h2 className="text-white text-xl font-mono mb-6">Skills - Programming Languages</h2>

      <div
        id="skills-container"
        className="relative overflow-hidden"
        style={{
          minHeight: '450px'
        }}
      >
        {coreSkills.flatMap((skill, skillIndex) =>
          Array.from({ length: 2 }, (_, instanceIndex) => (
            <FloatingSkill
              key={`${skill.name}-${instanceIndex}`}
              skill={skill}
              index={skillIndex * 2 + instanceIndex}
              isActive={selectedSkill?.name === skill.name}
              onClick={setSelectedSkill}
              containerBounds={containerBounds}
              allPositions={allPositions}
              updatePosition={updatePosition}
            />
          ))
        )}
      </div>

      {selectedSkill && (
        <div className="border border-gray-700 bg-black/50 p-4">
          <div className="text-white font-mono font-bold mb-2">{selectedSkill.name}</div>
          <div className="text-gray-300 text-sm font-mono mb-2">{selectedSkill.description}</div>
          <div className="text-xs text-gray-500 font-mono">
            Level: {selectedSkill.level}/5
          </div>
        </div>
      )}
    </div>
  );
};

export default Skills;