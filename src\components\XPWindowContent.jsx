import React from 'react';

const XPWindowContent = ({ children, title, className = "" }) => {
  return (
    <div className={`h-full flex flex-col ${className}`} style={{ fontFamily: 'Tahoma, sans-serif' }}>
      {/* Window content area with XP styling */}
      <div className="flex-1 overflow-auto p-4" style={{ 
        background: '#ece9d8',
        color: '#000000'
      }}>
        {/* Content wrapper that maintains the cyber theme but with XP window background */}
        <div className="h-full">
          {children}
        </div>
      </div>
    </div>
  );
};

export default XPWindowContent;
