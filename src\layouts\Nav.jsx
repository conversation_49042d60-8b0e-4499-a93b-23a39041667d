import React, { useState, useEffect } from 'react';

const Nav = () => {
    const [currentTime, setCurrentTime] = useState(new Date());

    useEffect(() => {
        const timer = setInterval(() => {
            setCurrentTime(new Date());
        }, 1000);
        return () => clearInterval(timer);
    }, []);

    const formatTime = (date) => {
        return date.toLocaleTimeString('en-US', {
            hour12: true,
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    };

    const CyberLogo = () => (
        <div className="relative flex items-center">
            <div className="relative">
                <div className="text-2xl font-bold text-cyan-400 font-mono tracking-wider relative z-10">
                    <span className="relative">
                        H
                        <div className="absolute -top-1 -right-1 w-2 h-2 bg-cyan-400 animate-pulse"></div>
                    </span>
                    <span className="text-blue-400 mx-1">◊</span>
                    <span className="relative">
                        M
                        <div className="absolute -bottom-1 -left-1 w-2 h-2 bg-blue-400 animate-pulse" style={{ animationDelay: '0.5s' }}></div>
                    </span>
                </div>

                <div className="absolute inset-0 text-2xl font-bold text-cyan-400/20 font-mono tracking-wider transform translate-x-0.5 translate-y-0.5">
                    H◊M
                </div>

                <div className="absolute -inset-2 border border-cyan-400/30 transform rotate-45 animate-pulse"></div>
                <div className="absolute -inset-1 border border-blue-400/20 transform -rotate-45 animate-pulse" style={{ animationDelay: '1s' }}></div>
            </div>

            <div className="ml-3 flex flex-col">
                <div className="flex items-center space-x-1">
                    <div className="w-1 h-1 bg-green-400 animate-pulse"></div>
                    <div className="w-1 h-1 bg-yellow-400 animate-pulse" style={{ animationDelay: '0.3s' }}></div>
                    <div className="w-1 h-1 bg-red-400 animate-pulse" style={{ animationDelay: '0.6s' }}></div>
                </div>
                <div className="text-xs text-cyan-400 font-mono mt-1">
                    SYS_ONLINE
                </div>
            </div>
        </div>
    );
    return (
        <div className="border-b-2 border-gray-700 bg-gradient-to-r from-gray-900 to-gray-800 p-4">
            <div className="flex justify-between items-center">
                <div className="flex items-center gap-4">
                    <CyberLogo />
                    <div className="text-sm">
                        UEFI BIOS Utility - HAFSA MOUSSAID
                        <div className="text-xs text-gray-300">System Ready</div>
                    </div>
                </div>
                <div className="text-right">
                    <div className="text-gray-300 text-sm">v2.0.4</div>
                    <div className="text-lg font-bold">{formatTime(currentTime)}</div>
                    <div className="text-xs text-gray-300">
                        {currentTime.toLocaleDateString()}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Nav;