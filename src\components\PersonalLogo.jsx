import React from 'react';

const PersonalLogo = ({ size = 20 }) => {
  return (
    <svg width={size} height={size} viewBox="0 0 32 32" fill="none">
      <defs>
        <linearGradient id="logoGrad" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#00ff88" />
          <stop offset="50%" stopColor="#00cc66" />
          <stop offset="100%" stopColor="#009944" />
        </linearGradient>
        <linearGradient id="logoGrad2" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#0088ff" />
          <stop offset="100%" stopColor="#0066cc" />
        </linearGradient>
      </defs>
      
      {/* Outer circle */}
      <circle cx="16" cy="16" r="15" fill="url(#logoGrad)" stroke="#006633" strokeWidth="1"/>
      
      {/* Inner design - Letter H for Hafsa */}
      <rect x="10" y="8" width="2" height="16" fill="white"/>
      <rect x="20" y="8" width="2" height="16" fill="white"/>
      <rect x="10" y="15" width="12" height="2" fill="white"/>
      
      {/* Cyber accent dots */}
      <circle cx="8" cy="8" r="1" fill="url(#logoGrad2)"/>
      <circle cx="24" cy="8" r="1" fill="url(#logoGrad2)"/>
      <circle cx="8" cy="24" r="1" fill="url(#logoGrad2)"/>
      <circle cx="24" cy="24" r="1" fill="url(#logoGrad2)"/>
    </svg>
  );
};

export default PersonalLogo;
