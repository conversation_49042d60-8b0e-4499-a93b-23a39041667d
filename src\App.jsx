import React, { useState, useEffect } from 'react';
import WindowsXPDesktop from './components/WindowsXPDesktop';
import WindowManager from './components/WindowManager';

const App = () => {
  const [openWindows, setOpenWindows] = useState([]);
  const [activeWindow, setActiveWindow] = useState(null);

  const handleOpenWindow = (windowId) => {
    // Check if window is already open
    const existingWindow = openWindows.find(w => w.id === windowId);
    if (existingWindow) {
      // Focus existing window
      setActiveWindow(windowId);
      return;
    }

    // Create new window
    const newWindow = {
      id: windowId,
      isMaximized: false,
      isMinimized: false
    };

    setOpenWindows([...openWindows, newWindow]);
    setActiveWindow(windowId);
  };

  const handleCloseWindow = (windowId) => {
    setOpenWindows(openWindows.filter(w => w.id !== windowId));
    if (activeWindow === windowId) {
      const remainingWindows = openWindows.filter(w => w.id !== windowId);
      setActiveWindow(remainingWindows.length > 0 ? remainingWindows[remainingWindows.length - 1].id : null);
    }
  };

  const handleMinimizeWindow = (windowId) => {
    setOpenWindows(openWindows.map(w =>
      w.id === windowId ? { ...w, isMinimized: !w.isMinimized } : w
    ));
  };

  const handleMaximizeWindow = (windowId) => {
    setOpenWindows(openWindows.map(w =>
      w.id === windowId ? { ...w, isMaximized: !w.isMaximized } : w
    ));
  };

  const handleFocusWindow = (windowId) => {
    setActiveWindow(windowId);
  };

  return (
    <div className="min-h-screen overflow-hidden">
      {/* Windows XP Desktop */}
      <WindowsXPDesktop
        onOpenWindow={handleOpenWindow}
        openWindows={openWindows}
        activeWindow={activeWindow}
        onFocusWindow={handleFocusWindow}
      />

      {/* Window Manager */}
      <WindowManager
        openWindows={openWindows}
        activeWindow={activeWindow}
        onCloseWindow={handleCloseWindow}
        onMinimizeWindow={handleMinimizeWindow}
        onMaximizeWindow={handleMaximizeWindow}
        onFocusWindow={handleFocusWindow}
      />
    </div>
  );
};

export default App;