import React, { useState, useEffect } from 'react';
import { Activity, User, Cpu, Code, FileText, Mail, Menu, X } from 'lucide-react';
import Main from './components/Main';
import About from './components/About';
import Skills from './components/Skills';
import Projects from './components/Projects';
import Resume from './components/Resume';
import Contact from './components/Contact';
import Nav from './layouts/Nav';

const App = () => {
  const [activeSection, setActiveSection] = useState('Main');
  const [currentTime, setCurrentTime] = useState(new Date());
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [openTabs, setOpenTabs] = useState(['Main']);


  const menuItems = [
    { name: 'Main', icon: <Activity size={16} /> },
    { name: 'About', icon: <User size={16} /> },
    { name: 'Skills', icon: <Cpu size={16} /> },
    { name: 'Projects', icon: <Code size={16} /> },
    { name: 'Resume', icon: <FileText size={16} /> },
    { name: 'Contact', icon: <Mail size={16} /> }
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  const handleSectionClick = (sectionName) => {
    setActiveSection(sectionName);
    if (!openTabs.includes(sectionName)) {
      setOpenTabs([...openTabs, sectionName]);
    }
    setIsMobileMenuOpen(false);
  };

  const closeTab = (tabName) => {
    const newTabs = openTabs.filter(tab => tab !== tabName);
    setOpenTabs(newTabs);
    if (activeSection === tabName) {
      setActiveSection(newTabs.length > 0 ? newTabs[newTabs.length - 1] : 'Main');
    }
  };

  const switchToTab = (tabName) => {
    setActiveSection(tabName);
  };

  const renderContent = () => {
    switch (activeSection) {
      case 'Main':
        return <Main setActiveSection={setActiveSection} />;
      case 'About':
        return <About />;
      case 'Skills':
        return <Skills />;
      case 'Projects':
        return <Projects />;
      case 'Resume':
        return <Resume />;
      case 'Contact':
        return <Contact />;
      default:
        return <Main setActiveSection={setActiveSection} />;
    }
  };

  return (
    <div className="min-h-screen bg-black text-white font-mono pb-12">
      <Nav></Nav>

      <div className="flex min-h-screen relative">
        {/* Mobile Menu Button */}
        <button
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          className="lg:hidden fixed top-4 left-4 z-50 bg-gray-900 border border-gray-700 p-2 rounded hover:bg-gray-800 transition-colors"
        >
          {isMobileMenuOpen ? <X size={20} /> : <Menu size={20} />}
        </button>

        {/* Mobile Overlay */}
        {isMobileMenuOpen && (
          <div
            className="lg:hidden fixed inset-0 bg-black/50 z-30"
            onClick={() => setIsMobileMenuOpen(false)}
          ></div>
        )}

        {/* Sidebar */}
        <div className={`
          w-64 bg-gray-900 border-r-2 border-gray-700 p-4 transition-transform duration-300 ease-in-out z-40
          lg:relative lg:translate-x-0
          ${isMobileMenuOpen ? 'fixed inset-y-0 left-0 translate-x-0' : 'fixed inset-y-0 left-0 -translate-x-full'}
        `}>
          <div className="space-y-2 mt-16 lg:mt-0">
            {menuItems.map((item) => (
              <button
                key={item.name}
                onClick={() => handleSectionClick(item.name)}
                className={`w-full flex items-center gap-3 p-3 text-left font-mono transition-all ${
                  activeSection === item.name
                    ? 'bg-gray-800 border-l-4 border-white text-white'
                    : 'text-gray-400 hover:bg-gray-800'
                }`}
              >
                {item.icon}
                {item.name}
              </button>
            ))}
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-3 md:p-6 overflow-y-auto lg:ml-0">
          {renderContent()}
        </div>
      </div>

      {/* Windows 7 Style Taskbar - Fixed */}
      <div className="fixed bottom-0 left-0 right-0 h-12 bg-gradient-to-b from-gray-200 via-gray-300 to-gray-400 border-t border-gray-500 flex items-center px-2 shadow-lg z-50">
        {/* Start Button */}
        <button
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          className="h-8 px-4 bg-gradient-to-b from-green-400 via-green-500 to-green-600 hover:from-green-300 hover:via-green-400 hover:to-green-500 rounded-sm border border-green-700 shadow-md flex items-center text-white font-bold text-sm transition-all duration-150 lg:hidden"
        >
          <div className="w-4 h-4 bg-white rounded-full mr-2 flex items-center justify-center">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          </div>
          Start
        </button>

        {/* Taskbar Tabs */}
        <div className="flex-1 flex items-center ml-2 space-x-1 overflow-x-auto">
          {openTabs.map((tab) => {
            const menuItem = menuItems.find(item => item.name === tab);
            const isActive = activeSection === tab;

            return (
              <div
                key={tab}
                className={`
                  relative h-8 min-w-[120px] max-w-[200px] flex items-center px-3 cursor-pointer transition-all duration-200
                  ${isActive
                    ? 'bg-gradient-to-b from-white via-gray-100 to-gray-200 border-t border-l border-r border-gray-400 shadow-inner'
                    : 'bg-gradient-to-b from-gray-300 via-gray-400 to-gray-500 hover:from-gray-200 hover:via-gray-300 hover:to-gray-400 border border-gray-600 shadow-sm'
                  }
                  rounded-t-sm
                `}
                onClick={() => switchToTab(tab)}
              >
                <div className="flex items-center space-x-2 flex-1 min-w-0">
                  <div className={`${isActive ? 'text-gray-800' : 'text-white'} flex-shrink-0`}>
                    {menuItem?.icon}
                  </div>
                  <span className={`${isActive ? 'text-gray-800' : 'text-white'} text-xs font-medium truncate`}>
                    {tab}
                  </span>
                </div>

                {/* Close Button */}
                {openTabs.length > 1 && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      closeTab(tab);
                    }}
                    className={`
                      ml-2 w-4 h-4 flex items-center justify-center rounded-sm transition-colors flex-shrink-0
                      ${isActive
                        ? 'hover:bg-red-500 hover:text-white text-gray-600'
                        : 'hover:bg-red-600 hover:text-white text-gray-200'
                      }
                    `}
                  >
                    <X size={10} />
                  </button>
                )}
              </div>
            );
          })}
        </div>

        {/* System Tray */}
        <div className="flex items-center space-x-2 ml-4">
          {/* Quick Actions */}
          <div className="hidden md:flex items-center space-x-1">
            <button
              onClick={() => handleSectionClick('Contact')}
              className="h-6 w-6 bg-gradient-to-b from-gray-300 to-gray-500 hover:from-gray-200 hover:to-gray-400 border border-gray-600 rounded-sm flex items-center justify-center transition-all"
              title="Contact"
            >
              <Mail size={12} className="text-white" />
            </button>
            <a
              href="https://github.com/07asfah"
              target="_blank"
              rel="noopener noreferrer"
              className="h-6 w-6 bg-gradient-to-b from-gray-300 to-gray-500 hover:from-gray-200 hover:to-gray-400 border border-gray-600 rounded-sm flex items-center justify-center transition-all"
              title="GitHub"
            >
              <Code size={12} className="text-white" />
            </a>
          </div>

          {/* Clock */}
          <div className="bg-gradient-to-b from-gray-200 to-gray-400 border border-gray-500 px-2 py-1 rounded-sm shadow-inner">
            <div className="text-xs font-medium text-gray-800 text-center leading-tight">
              <div>{currentTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</div>
              <div className="text-[10px]">{currentTime.toLocaleDateString([], { month: 'short', day: 'numeric' })}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default App;