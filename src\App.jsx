import React, { useState, useEffect } from 'react';
import WindowsXPDesktop from './components/WindowsXPDesktop';
import WindowManager from './components/WindowManager';

const App = () => {
  const [openWindows, setOpenWindows] = useState([]);
  const [activeWindow, setActiveWindow] = useState(null);
  const [currentWallpaper, setCurrentWallpaper] = useState({
    id: 'xp-default',
    name: 'Windows XP Default',
    path: './assets/Images/Windows XP ❤ 4K HD Desktop Wallpaper for 4K Ultra HD TV • Wide.jpg'
  });
  const [isShutdown, setIsShutdown] = useState(false);

  const handleOpenWindow = (windowId) => {
    // Check if window is already open
    const existingWindow = openWindows.find(w => w.id === windowId);
    if (existingWindow) {
      // Focus existing window
      setActiveWindow(windowId);
      return;
    }

    // Create new window - open maximized by default
    const newWindow = {
      id: windowId,
      isMaximized: true,
      isMinimized: false
    };

    setOpenWindows([...openWindows, newWindow]);
    setActiveWindow(windowId);
  };

  const handleCloseWindow = (windowId) => {
    setOpenWindows(openWindows.filter(w => w.id !== windowId));
    if (activeWindow === windowId) {
      const remainingWindows = openWindows.filter(w => w.id !== windowId);
      setActiveWindow(remainingWindows.length > 0 ? remainingWindows[remainingWindows.length - 1].id : null);
    }
  };

  const handleMinimizeWindow = (windowId) => {
    setOpenWindows(openWindows.map(w =>
      w.id === windowId ? { ...w, isMinimized: !w.isMinimized } : w
    ));
  };

  const handleMaximizeWindow = (windowId) => {
    setOpenWindows(openWindows.map(w =>
      w.id === windowId ? { ...w, isMaximized: !w.isMaximized } : w
    ));
  };

  const handleFocusWindow = (windowId) => {
    setActiveWindow(windowId);
  };

  const handleWallpaperChange = (wallpaper) => {
    setCurrentWallpaper(wallpaper);
  };

  const handleLogOff = () => {
    // Close the current browser tab/window
    window.close();
  };

  const handleShutdown = () => {
    setIsShutdown(true);
  };

  if (isShutdown) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center text-white">
          <div className="text-4xl font-bold mb-4">Windows is shutting down...</div>
          <div className="text-lg">It's now safe to close this window.</div>
          <div className="mt-8">
            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-white mx-auto"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen overflow-hidden">
      {/* Windows XP Desktop */}
      <WindowsXPDesktop
        onOpenWindow={handleOpenWindow}
        openWindows={openWindows}
        activeWindow={activeWindow}
        onFocusWindow={handleFocusWindow}
        currentWallpaper={currentWallpaper}
        onLogOff={handleLogOff}
        onShutdown={handleShutdown}
      />

      {/* Window Manager */}
      <WindowManager
        openWindows={openWindows}
        activeWindow={activeWindow}
        onCloseWindow={handleCloseWindow}
        onMinimizeWindow={handleMinimizeWindow}
        onMaximizeWindow={handleMaximizeWindow}
        onFocusWindow={handleFocusWindow}
        onWallpaperChange={handleWallpaperChange}
        currentWallpaper={currentWallpaper}
      />
    </div>
  );
};

export default App;