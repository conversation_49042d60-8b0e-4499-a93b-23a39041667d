import React, { useState, useEffect } from 'react';
import WindowsXPDesktop from './components/WindowsXPDesktop';
import WindowManager from './components/WindowManager';
import WindowsXPShutdown from './components/WindowsXPShutdown';

const App = () => {
  const [openWindows, setOpenWindows] = useState([]);
  const [activeWindow, setActiveWindow] = useState(null);
  const [currentWallpaper, setCurrentWallpaper] = useState({
    id: 'xp-default',
    name: 'Windows XP Default',
    path: './assets/Images/Windows XP ❤ 4K HD Desktop Wallpaper for 4K Ultra HD TV • Wide.jpg'
  });
  const [isShutdown, setIsShutdown] = useState(false);

  const handleOpenWindow = (windowId) => {
    // Check if window is already open
    const existingWindow = openWindows.find(w => w.id === windowId);
    if (existingWindow) {
      // Focus existing window
      setActiveWindow(windowId);
      return;
    }

    // Create new window - open maximized by default
    const newWindow = {
      id: windowId,
      isMaximized: true,
      isMinimized: false
    };

    setOpenWindows([...openWindows, newWindow]);
    setActiveWindow(windowId);
  };

  const handleCloseWindow = (windowId) => {
    setOpenWindows(openWindows.filter(w => w.id !== windowId));
    if (activeWindow === windowId) {
      const remainingWindows = openWindows.filter(w => w.id !== windowId);
      setActiveWindow(remainingWindows.length > 0 ? remainingWindows[remainingWindows.length - 1].id : null);
    }
  };

  const handleMinimizeWindow = (windowId) => {
    setOpenWindows(openWindows.map(w =>
      w.id === windowId ? { ...w, isMinimized: !w.isMinimized } : w
    ));
  };

  const handleMaximizeWindow = (windowId) => {
    setOpenWindows(openWindows.map(w =>
      w.id === windowId ? { ...w, isMaximized: !w.isMaximized } : w
    ));
  };

  const handleFocusWindow = (windowId) => {
    // If window is minimized, restore it first
    setOpenWindows(openWindows.map(w =>
      w.id === windowId ? { ...w, isMinimized: false } : w
    ));
    setActiveWindow(windowId);
  };

  const handleWallpaperChange = (wallpaper) => {
    setCurrentWallpaper(wallpaper);
  };

  const handleLogOff = () => {
    // Close the current browser tab/window
    window.close();
  };

  const handleShutdown = () => {
    setIsShutdown(true);
  };

  if (isShutdown) {
    return <WindowsXPShutdown />;
  }

  return (
    <div className="min-h-screen overflow-hidden">
      {/* Windows XP Desktop */}
      <WindowsXPDesktop
        onOpenWindow={handleOpenWindow}
        openWindows={openWindows}
        activeWindow={activeWindow}
        onFocusWindow={handleFocusWindow}
        currentWallpaper={currentWallpaper}
        onLogOff={handleLogOff}
        onShutdown={handleShutdown}
      />

      {/* Window Manager */}
      <WindowManager
        openWindows={openWindows}
        activeWindow={activeWindow}
        onCloseWindow={handleCloseWindow}
        onMinimizeWindow={handleMinimizeWindow}
        onMaximizeWindow={handleMaximizeWindow}
        onFocusWindow={handleFocusWindow}
        onWallpaperChange={handleWallpaperChange}
        currentWallpaper={currentWallpaper}
      />
    </div>
  );
};

export default App;