import React, { useState } from 'react';
import { Monitor, Image, Settings, Palette, Grid, Move } from 'lucide-react';

const ControlPanel = () => {
  const [activeTab, setActiveTab] = useState('desktop');

  const tabs = [
    { id: 'desktop', name: 'Desktop', icon: <Grid size={16} /> },
    { id: 'appearance', name: 'Appearance', icon: <Palette size={16} /> },
    { id: 'system', name: 'System', icon: <Settings size={16} /> }
  ];

  const renderSystemTab = () => (
    <div className="p-4">
      <h3 className="font-bold mb-4 text-gray-800">System Information</h3>
      <div className="space-y-4">
        <div className="bg-blue-50 p-4 rounded border-l-4 border-blue-400">
          <div className="font-medium text-blue-800">Portfolio System</div>
          <div className="text-sm text-blue-700 mt-2 space-y-1">
            <div>• Developer: <PERSON><PERSON><PERSON></div>
            <div>• Theme: Windows XP Cyber Edition</div>
            <div>• Version: 2.0.4</div>
            <div>• Status: SYS_ONLINE</div>
          </div>
        </div>
        <div className="bg-green-50 p-4 rounded border-l-4 border-green-400">
          <div className="font-medium text-green-800">Features Active</div>
          <div className="text-sm text-green-700 mt-2 space-y-1">
            <div>✓ Draggable desktop icons</div>
            <div>✓ Windows XP authentic interface</div>
            <div>✓ Cyber-themed portfolio content</div>
            <div>✓ Interactive window management</div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderDesktopTab = () => (
    <div className="p-4">
      <h3 className="font-bold mb-4 text-gray-800">Desktop Icons</h3>
      <div className="space-y-4">
        <div className="flex items-center space-x-3">
          <Move size={20} className="text-blue-600" />
          <div>
            <div className="font-medium">Arrange Icons</div>
            <div className="text-sm text-gray-600">Drag and drop icons to rearrange them on your desktop</div>
          </div>
        </div>
        <div className="bg-blue-50 p-3 rounded border-l-4 border-blue-400">
          <div className="font-medium text-blue-800">How to move icons:</div>
          <ul className="text-sm text-blue-700 mt-1 space-y-1">
            <li>• Click and hold any desktop icon</li>
            <li>• Drag it to your desired position</li>
            <li>• Release to place the icon</li>
          </ul>
        </div>
      </div>
    </div>
  );

  const renderAppearanceTab = () => (
    <div className="p-4">
      <h3 className="font-bold mb-4 text-gray-800">Visual Style</h3>
      <div className="space-y-4">
        <div className="flex items-center space-x-3">
          <Palette size={20} className="text-purple-600" />
          <div>
            <div className="font-medium">Windows XP Theme</div>
            <div className="text-sm text-gray-600">Classic Windows XP visual style is active</div>
          </div>
        </div>
        <div className="bg-green-50 p-3 rounded border-l-4 border-green-400">
          <div className="font-medium text-green-800">Theme Features:</div>
          <ul className="text-sm text-green-700 mt-1 space-y-1">
            <li>• Authentic Windows XP taskbar</li>
            <li>• Classic window borders and buttons</li>
            <li>• XP-style start menu</li>
            <li>• Cyber-themed portfolio content</li>
          </ul>
        </div>
      </div>
    </div>
  );

  return (
    <div className="h-full flex flex-col" style={{ fontFamily: 'Tahoma, sans-serif' }}>
      {/* Control Panel Header */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-3">
        <div className="flex items-center space-x-2">
          <Settings size={20} />
          <h2 className="font-bold">Display Properties</h2>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex border-b border-gray-300 bg-gray-100">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`flex items-center space-x-2 px-4 py-2 border-b-2 transition-colors ${
              activeTab === tab.id
                ? 'border-blue-500 bg-white text-blue-600'
                : 'border-transparent hover:bg-gray-200'
            }`}
          >
            {tab.icon}
            <span className="text-sm font-medium">{tab.name}</span>
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="flex-1 bg-white overflow-auto">
        {activeTab === 'desktop' && renderDesktopTab()}
        {activeTab === 'appearance' && renderAppearanceTab()}
        {activeTab === 'system' && renderSystemTab()}
      </div>

      {/* Footer */}
      <div className="bg-gray-100 p-3 border-t border-gray-300 flex justify-end space-x-2">
        <button className="xp-button">
          OK
        </button>
        <button className="xp-button">
          Cancel
        </button>
        <button className="xp-button">
          Apply
        </button>
      </div>
    </div>
  );
};

export default ControlPanel;
