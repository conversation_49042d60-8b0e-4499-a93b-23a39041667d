import React, { useState } from 'react';
import { <PERSON>, <PERSON>, Settings, Palette, Grid, Move } from 'lucide-react';

const ControlPanel = ({ onWallpaperChange, currentWallpaper }) => {
  const [activeTab, setActiveTab] = useState('display');

  const wallpapers = [
    {
      id: 'xp-default',
      name: 'Windows XP Default',
      path: './assets/Images/Windows XP ❤ 4K HD Desktop Wallpaper for 4K Ultra HD TV • Wide.jpg',
      preview: './assets/Images/Windows XP ❤ 4K HD Desktop Wallpaper for 4K Ultra HD TV • Wide.jpg'
    },
    {
      id: 'cyber-blue',
      name: 'Cyber Blue',
      path: 'linear-gradient(135deg, #0f3460 0%, #0d2946 50%, #0a1e35 100%)',
      preview: 'linear-gradient(135deg, #0f3460 0%, #0d2946 50%, #0a1e35 100%)'
    },
    {
      id: 'cyber-green',
      name: '<PERSON> Green',
      path: 'linear-gradient(135deg, #0d4f3c 0%, #0a3d2e 50%, #072b20 100%)',
      preview: 'linear-gradient(135deg, #0d4f3c 0%, #0a3d2e 50%, #072b20 100%)'
    },
    {
      id: 'cyber-purple',
      name: 'Cyber Purple',
      path: 'linear-gradient(135deg, #4a0e4e 0%, #3a0b3e 50%, #2a082e 100%)',
      preview: 'linear-gradient(135deg, #4a0e4e 0%, #3a0b3e 50%, #2a082e 100%)'
    },
    {
      id: 'solid-black',
      name: 'Solid Black',
      path: '#000000',
      preview: '#000000'
    }
  ];

  const tabs = [
    { id: 'display', name: 'Display', icon: <Monitor size={16} /> },
    { id: 'desktop', name: 'Desktop', icon: <Grid size={16} /> },
    { id: 'appearance', name: 'Appearance', icon: <Palette size={16} /> }
  ];

  const handleWallpaperSelect = (wallpaper) => {
    onWallpaperChange(wallpaper);
  };

  const renderDisplayTab = () => (
    <div className="p-4">
      <h3 className="font-bold mb-4 text-gray-800">Desktop Background</h3>
      <div className="grid grid-cols-2 gap-4 mb-4">
        {wallpapers.map((wallpaper) => (
          <div
            key={wallpaper.id}
            className={`border-2 p-2 cursor-pointer transition-all ${
              currentWallpaper?.id === wallpaper.id 
                ? 'border-blue-500 bg-blue-50' 
                : 'border-gray-300 hover:border-gray-400'
            }`}
            onClick={() => handleWallpaperSelect(wallpaper)}
          >
            <div
              className="w-full h-20 mb-2 border border-gray-300"
              style={{
                background: wallpaper.path.startsWith('linear-gradient') || wallpaper.path.startsWith('#')
                  ? wallpaper.path
                  : `url('${wallpaper.path}')`,
                backgroundSize: 'cover',
                backgroundPosition: 'center'
              }}
            />
            <div className="text-xs text-center font-medium">{wallpaper.name}</div>
          </div>
        ))}
      </div>
      <div className="text-sm text-gray-600">
        <p>Click on a wallpaper to apply it to your desktop.</p>
      </div>
    </div>
  );

  const renderDesktopTab = () => (
    <div className="p-4">
      <h3 className="font-bold mb-4 text-gray-800">Desktop Icons</h3>
      <div className="space-y-4">
        <div className="flex items-center space-x-3">
          <Move size={20} className="text-blue-600" />
          <div>
            <div className="font-medium">Arrange Icons</div>
            <div className="text-sm text-gray-600">Drag and drop icons to rearrange them on your desktop</div>
          </div>
        </div>
        <div className="bg-blue-50 p-3 rounded border-l-4 border-blue-400">
          <div className="font-medium text-blue-800">How to move icons:</div>
          <ul className="text-sm text-blue-700 mt-1 space-y-1">
            <li>• Click and hold any desktop icon</li>
            <li>• Drag it to your desired position</li>
            <li>• Release to place the icon</li>
          </ul>
        </div>
      </div>
    </div>
  );

  const renderAppearanceTab = () => (
    <div className="p-4">
      <h3 className="font-bold mb-4 text-gray-800">Visual Style</h3>
      <div className="space-y-4">
        <div className="flex items-center space-x-3">
          <Palette size={20} className="text-purple-600" />
          <div>
            <div className="font-medium">Windows XP Theme</div>
            <div className="text-sm text-gray-600">Classic Windows XP visual style is active</div>
          </div>
        </div>
        <div className="bg-green-50 p-3 rounded border-l-4 border-green-400">
          <div className="font-medium text-green-800">Theme Features:</div>
          <ul className="text-sm text-green-700 mt-1 space-y-1">
            <li>• Authentic Windows XP taskbar</li>
            <li>• Classic window borders and buttons</li>
            <li>• XP-style start menu</li>
            <li>• Cyber-themed portfolio content</li>
          </ul>
        </div>
      </div>
    </div>
  );

  return (
    <div className="h-full flex flex-col" style={{ fontFamily: 'Tahoma, sans-serif' }}>
      {/* Control Panel Header */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-3">
        <div className="flex items-center space-x-2">
          <Settings size={20} />
          <h2 className="font-bold">Display Properties</h2>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex border-b border-gray-300 bg-gray-100">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`flex items-center space-x-2 px-4 py-2 border-b-2 transition-colors ${
              activeTab === tab.id
                ? 'border-blue-500 bg-white text-blue-600'
                : 'border-transparent hover:bg-gray-200'
            }`}
          >
            {tab.icon}
            <span className="text-sm font-medium">{tab.name}</span>
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="flex-1 bg-white overflow-auto">
        {activeTab === 'display' && renderDisplayTab()}
        {activeTab === 'desktop' && renderDesktopTab()}
        {activeTab === 'appearance' && renderAppearanceTab()}
      </div>

      {/* Footer */}
      <div className="bg-gray-100 p-3 border-t border-gray-300 flex justify-end space-x-2">
        <button className="xp-button">
          OK
        </button>
        <button className="xp-button">
          Cancel
        </button>
        <button className="xp-button">
          Apply
        </button>
      </div>
    </div>
  );
};

export default ControlPanel;
