import React, { useState } from 'react';
import XPWindow from './XPWindow';
import XPWindowContent from './XPWindowContent';
import ControlPanel from './ControlPanel';
import About from './About';
import Skills from './Skills';
import Projects from './Projects';
import Resume from './Resume';
import Contact from './Contact';
import Main from './Main';

const WindowManager = ({ openWindows, onCloseWindow, onMinimizeWindow, onMaximizeWindow, onFocusWindow, activeWindow, onWallpaperChange, currentWallpaper }) => {
  const getWindowContent = (windowId) => {
    const content = (() => {
      switch (windowId) {
        case 'about':
          return <About />;
        case 'skills':
          return <Skills />;
        case 'projects':
          return <Projects />;
        case 'resume':
          return <Resume />;
        case 'contact':
          return <Contact />;
        case 'portfolio':
          return <Main setActiveSection={() => {}} />;
        case 'settings':
          return <ControlPanel onWallpaperChange={onWallpaperChange} currentWallpaper={currentWallpaper} />;
        default:
          return <div className="p-4 text-center">Unknown window</div>;
      }
    })();

    // For Control Panel, don't wrap in XPWindowContent to avoid double styling
    if (windowId === 'settings') {
      return content;
    }

    return (
      <XPWindowContent title={getWindowTitle(windowId)}>
        {content}
      </XPWindowContent>
    );
  };

  const getWindowTitle = (windowId) => {
    const titles = {
      about: 'About Me - Portfolio',
      skills: 'Skills - Portfolio',
      projects: 'Projects - Portfolio',
      resume: 'Resume - Portfolio',
      contact: 'Contact - Portfolio',
      portfolio: 'My Portfolio - Main',
      settings: 'Display Properties'
    };
    return titles[windowId] || 'Unknown Window';
  };

  const getInitialPosition = (windowId, index) => {
    const baseX = 50 + (index * 30);
    const baseY = 50 + (index * 30);
    return { x: baseX, y: baseY };
  };

  const getInitialSize = (windowId) => {
    const sizes = {
      about: { width: 700, height: 500 },
      skills: { width: 800, height: 600 },
      projects: { width: 900, height: 700 },
      resume: { width: 600, height: 800 },
      contact: { width: 500, height: 400 },
      portfolio: { width: 1000, height: 700 }
    };
    return sizes[windowId] || { width: 600, height: 400 };
  };

  return (
    <>
      {openWindows.map((window, index) => (
        <XPWindow
          key={window.id}
          id={window.id}
          title={getWindowTitle(window.id)}
          isActive={activeWindow === window.id}
          isMaximized={window.isMaximized}
          initialPosition={getInitialPosition(window.id, index)}
          initialSize={getInitialSize(window.id)}
          onClose={() => onCloseWindow(window.id)}
          onMinimize={() => onMinimizeWindow(window.id)}
          onMaximize={() => onMaximizeWindow(window.id)}
        >
          <div 
            onClick={() => onFocusWindow(window.id)}
            className="h-full"
          >
            {getWindowContent(window.id)}
          </div>
        </XPWindow>
      ))}
    </>
  );
};

export default WindowManager;
