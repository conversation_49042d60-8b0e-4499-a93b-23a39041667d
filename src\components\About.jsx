import React, { useState, useEffect } from 'react';
import { User, Cpu, Monitor, Code, Coffee, Zap, Activity, Terminal, Eye, Brain } from 'lucide-react';


const MatrixRain = ({ active }) => {
  const [drops, setDrops] = useState([]);

  useEffect(() => {
    if (!active) return;

    const interval = setInterval(() => {
      setDrops(prev => {
        const newDrops = [...prev];
        if (Math.random() > 0.7) {
          newDrops.push({
            id: Date.now() + Math.random(),
            left: Math.random() * 100,
            char: String.fromCharCode(33 + Math.random() * 93)
          });
        }
        return newDrops.slice(-12);
      });
    }, 150);

    return () => clearInterval(interval);
  }, [active]);

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {drops.map((drop) => (
        <div
          key={drop.id}
          className="absolute text-green-400 opacity-40 animate-pulse font-mono text-xs"
          style={{
            left: `${drop.left}%`,
            top: '10px',
            animation: 'fall 3s linear forwards'
          }}
        >
          {drop.char}
        </div>
      ))}
      <style jsx>{`
        @keyframes fall {
          to {
            transform: translateY(300px);
            opacity: 0;
          }
        }
      `}</style>
    </div>
  );
};


const BlinkingDot = ({ active = true, color = "bg-green-400" }) => (
  <div className={`w-2 h-2 rounded-full ${color} ${active ? 'animate-pulse' : ''}`} />
);

const BootSequence = () => {
  const [currentLine, setCurrentLine] = useState(0);
  const bootLines = [
    "INITIALIZING DEVELOPER PROFILE...",
    "LOADING CORE MODULES... OK",
    "SCANNING SKILL DATABASE... OK",
    "VERIFYING EXPERIENCE LOGS... OK",
    "SYSTEM READY - HAFSA.EXE LOADED"
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentLine(prev => (prev + 1) % (bootLines.length + 1));
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  return (
    <div className="bg-black border border-green-400 p-3 font-mono text-xs">
      <div className="text-green-400 mb-2">SYSTEM BOOT SEQUENCE v2.0.4</div>
      {bootLines.slice(0, currentLine).map((line, i) => (
        <div key={i} className="text-green-300 flex justify-between">
          <span>{line}</span>
          <span className="text-green-400">[OK]</span>
        </div>
      ))}
      {currentLine < bootLines.length && (
        <div className="text-green-300 flex">
          <span>{bootLines[currentLine]}</span>
          <span className="animate-pulse ml-2">█</span>
        </div>
      )}
    </div>
  );
};

const AnimatedProgressBar = ({ label, percentage, color = "green" }) => {
  const [currentPercentage, setCurrentPercentage] = useState(0);

  useEffect(() => {
    const timer = setTimeout(() => {
      setCurrentPercentage(percentage);
    }, 500);
    return () => clearTimeout(timer);
  }, [percentage]);

  const colorClasses = {
    green: "bg-green-400 border-green-400 text-green-400",
    blue: "bg-blue-400 border-blue-400 text-blue-400",
    yellow: "bg-yellow-400 border-yellow-400 text-yellow-400",
    purple: "bg-purple-400 border-purple-400 text-purple-400",
    red: "bg-red-400 border-red-400 text-red-400"
  };

  return (
    <div className="space-y-2">
      <div className="flex justify-between items-center">
        <span className={`font-mono text-xs ${colorClasses[color].split(' ')[2]}`}>{label}</span>
        <span className={`font-mono text-xs ${colorClasses[color].split(' ')[2]}`}>{currentPercentage}%</span>
      </div>
      <div className={`w-full h-2 border ${colorClasses[color].split(' ')[1]} bg-black`}>
        <div
          className={`h-full ${colorClasses[color].split(' ')[0]} transition-all duration-2000 ease-out`}
          style={{ width: `${currentPercentage}%` }}
        />
      </div>
    </div>
  );
};


const GlitchAvatar = () => {
  const [glitching, setGlitching] = useState(false);

  useEffect(() => {
    const interval = setInterval(() => {
      setGlitching(true);
      setTimeout(() => setGlitching(false), 200);
    }, 3000);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="relative">
      <div className={`w-32 h-32 border-2 border-cyan-400 bg-gradient-to-br from-cyan-900/50 to-blue-900/50 flex items-center justify-center ${glitching ? 'animate-pulse' : ''}`}>
        <User size={48} className={`text-cyan-400 ${glitching ? 'animate-bounce' : ''}`} />
        {glitching && (
          <div className="absolute inset-0 bg-cyan-400/20 animate-ping" />
        )}
      </div>
      <div className="absolute -bottom-2 -right-2 bg-green-400 w-6 h-6 rounded-full flex items-center justify-center">
        <div className="w-3 h-3 bg-black rounded-full animate-pulse" />
      </div>
    </div>
  );
};

const About = () => {
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 1000);
    return () => clearInterval(timer);
  }, []);

  return (
    <div className="space-y-6">

      <div className="relative border border-green-400 bg-black/90 p-4 overflow-hidden">
        <MatrixRain active={true} />
        <div className="relative z-10 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Terminal className="text-green-400" size={24} />
            <h2 className="text-green-400 text-xl font-mono">DEVELOPER_PROFILE.EXE</h2>
            <BlinkingDot />
          </div>
          <div className="text-green-400 font-mono text-sm">
            {currentTime.toLocaleTimeString()}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">

        <div className="space-y-6">
          <div className="border border-cyan-400 bg-black/90 p-6 text-center">
            <div className="flex items-center gap-2 mb-4">
              <Eye className="text-cyan-400" size={16} />
              <span className="text-cyan-400 font-mono text-sm">VISUAL_ID</span>
              <BlinkingDot color="bg-cyan-400" />
            </div>
            <GlitchAvatar />
            <div className="mt-4 text-cyan-300 font-mono text-sm">
              HAFSA_MOUSSAID.DEV
            </div>
            <div className="text-xs text-cyan-500 font-mono">
              STATUS: ONLINE • READY_FOR_HIRE
            </div>
          </div>

          <BootSequence />
        </div>


        <div className="lg:col-span-2 space-y-6">

          <div className="border border-blue-400 bg-black/90 p-6 relative">
            <div className="flex items-center gap-2 mb-4">
              <Cpu className="text-blue-400" size={20} />
              <h3 className="text-blue-400 font-mono">CORE_SYSTEM_INFO</h3>
              <BlinkingDot color="bg-blue-400" />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm font-mono">
              <div className="space-y-2">
                <div className="text-blue-300">
                  <span className="text-blue-400">SYSTEM_NAME:</span> Hafsa Moussaid
                </div>
                <div className="text-blue-300">
                  <span className="text-blue-400">ARCHITECTURE:</span> Full-Stack Developer
                </div>
                <div className="text-blue-300">
                  <span className="text-blue-400">PRIMARY_FUNCTION:</span> Building Websites
                </div>
                <div className="text-blue-300">
                  <span className="text-blue-400">COMPATIBLE_OS:</span> Web, Mobile, Desktop
                </div>
              </div>
              <div className="space-y-2">
                <div className="text-blue-300">
                  <span className="text-blue-400">SECURITY_LEVEL:</span> Clean Code Practices
                </div>
                <div className="text-blue-300">
                  <span className="text-blue-400">UPDATE_FREQ:</span> Continuous Learning
                </div>
                <div className="text-blue-300">
                  <span className="text-blue-400">STATUS:</span> Active, Seeking Challenges
                </div>
                <div className="text-blue-300">
                  <span className="text-blue-400">UPTIME:</span> 24/7 Available
                </div>
              </div>
            </div>
          </div>

          <div className="border border-yellow-400 bg-black/90 p-6">
            <div className="flex items-center gap-2 mb-4">
              <Activity className="text-yellow-400" size={20} />
              <h3 className="text-yellow-400 font-mono">PERFORMANCE_METRICS</h3>
              <BlinkingDot color="bg-yellow-400" />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <AnimatedProgressBar label="PROBLEM_SOLVING" percentage={92} color="green" />
                <AnimatedProgressBar label="CODE_QUALITY" percentage={88} color="blue" />
                <AnimatedProgressBar label="CREATIVITY" percentage={85} color="purple" />
              </div>
              <div className="space-y-4">
                <AnimatedProgressBar label="TEAMWORK" percentage={90} color="yellow" />
                <AnimatedProgressBar label="LEARNING_SPEED" percentage={95} color="green" />
                <AnimatedProgressBar label="DEBUGGING_SKILLS" percentage={87} color="red" />
              </div>
            </div>
          </div>

          <div className="border border-purple-400 bg-black/90 p-6">
            <div className="flex items-center gap-2 mb-4">
              <Brain className="text-purple-400" size={20} />
              <h3 className="text-purple-400 font-mono">SYSTEM_RESOURCES</h3>
              <BlinkingDot color="bg-purple-400" />
            </div>

            <div className="grid grid-cols-3 gap-4 text-center">
              <div className="border border-purple-400/50 p-3">
                <Coffee className="text-purple-400 mx-auto mb-2" size={24} />
                <div className="text-purple-400 font-mono text-lg font-bold">∞</div>
                <div className="text-purple-300 font-mono text-xs">CAFFEINE_LEVEL</div>
              </div>
              <div className="border border-purple-400/50 p-3">
                <Zap className="text-purple-400 mx-auto mb-2" size={24} />
                <div className="text-purple-400 font-mono text-lg font-bold">100%</div>
                <div className="text-purple-300 font-mono text-xs">MOTIVATION</div>
              </div>
              <div className="border border-purple-400/50 p-3">
                <Code className="text-purple-400 mx-auto mb-2" size={24} />
                <div className="text-purple-400 font-mono text-lg font-bold">24/7</div>
                <div className="text-purple-300 font-mono text-xs">CODING_MODE</div>
              </div>
            </div>
          </div>
        </div>
      </div>


      <div className="border border-gray-700 bg-black/30 p-3 text-center">
        <div className="text-gray-400 font-mono text-sm">
          <span className="animate-pulse">█</span> DEVELOPER_PROFILE_LOADED_SUCCESSFULLY <span className="animate-pulse">█</span>
        </div>
        <div className="text-xs text-gray-500 font-mono mt-1">
          LAST_COMPILE: {new Date().toLocaleDateString()} • STATUS: READY_FOR_DEPLOYMENT
        </div>
      </div>
    </div>
  );
};

export default About;