import React, { useState, useEffect } from 'react';

// Your Cyber Logo Component
const CyberLogoLarge = () => (
  <div className="relative flex items-center justify-center">
    <div className="relative">
      <div className="text-4xl font-bold text-cyan-400 font-mono tracking-wider relative z-10">
        <span className="relative">
          H
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-cyan-400 animate-pulse"></div>
        </span>
        <span className="text-blue-400 mx-2">◊</span>
        <span className="relative">
          M
          <div className="absolute -bottom-1 -left-1 w-3 h-3 bg-blue-400 animate-pulse" style={{ animationDelay: '0.5s' }}></div>
        </span>
      </div>

      {/* Shadow/Depth Layer */}
      <div className="absolute inset-0 text-4xl font-bold text-cyan-400/20 font-mono tracking-wider transform translate-x-1 translate-y-1">
        H◊M
      </div>

      {/* Geometric Border Animations */}
      <div className="absolute -inset-4 border border-cyan-400/30 transform rotate-45 animate-pulse"></div>
      <div
        className="absolute -inset-2 border border-blue-400/20 transform -rotate-45 animate-pulse"
        style={{ animationDelay: '1s' }}
      ></div>
    </div>

    {/* Status Indicators */}
    <div className="ml-6 flex flex-col">
      {/* Status Lights */}
      <div className="flex items-center space-x-2">
        <div className="w-2 h-2 bg-green-400 animate-pulse"></div>
        <div
          className="w-2 h-2 bg-yellow-400 animate-pulse"
          style={{ animationDelay: '0.3s' }}
        ></div>
        <div
          className="w-2 h-2 bg-red-400 animate-pulse"
          style={{ animationDelay: '0.6s' }}
        ></div>
      </div>

      {/* Status Text */}
      <div className="text-sm text-cyan-400 font-mono mt-2">
        SYS_OFFLINE
      </div>
    </div>
  </div>
);

const WindowsXPShutdown = () => {
  const [shutdownStage, setShutdownStage] = useState('saving');
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    const stages = [
      { stage: 'saving', duration: 2000, message: 'Saving your settings...' },
      { stage: 'closing', duration: 1500, message: 'Windows is shutting down...' },
      { stage: 'final', duration: 1000, message: 'It\'s now safe to turn off your computer.' }
    ];

    let currentStageIndex = 0;
    let progressInterval;

    const nextStage = () => {
      if (currentStageIndex < stages.length) {
        const currentStage = stages[currentStageIndex];
        setShutdownStage(currentStage.stage);
        setProgress(0);

        // Animate progress bar
        progressInterval = setInterval(() => {
          setProgress(prev => {
            if (prev >= 100) {
              clearInterval(progressInterval);
              currentStageIndex++;
              setTimeout(nextStage, 500);
              return 100;
            }
            return prev + (100 / (currentStage.duration / 50));
          });
        }, 50);
      }
    };

    nextStage();

    return () => {
      if (progressInterval) clearInterval(progressInterval);
    };
  }, []);

  const getShutdownContent = () => {
    switch (shutdownStage) {
      case 'saving':
        return {
          title: 'Saving your settings...',
          subtitle: 'Please wait while Windows saves your personal settings.',
          showProgress: true,
          showSpinner: true
        };
      case 'closing':
        return {
          title: 'Windows is shutting down...',
          subtitle: 'Please wait while Windows closes all programs.',
          showProgress: true,
          showSpinner: true
        };
      case 'final':
        return {
          title: 'It\'s now safe to turn off your computer.',
          subtitle: 'You can now close this window or turn off your computer.',
          showProgress: false,
          showSpinner: false
        };
      default:
        return {
          title: 'Shutting down...',
          subtitle: '',
          showProgress: true,
          showSpinner: true
        };
    }
  };

  const content = getShutdownContent();

  return (
    <div className="fixed inset-0 z-[9999] bg-gradient-to-b from-blue-800 via-blue-900 to-blue-950 flex items-center justify-center">
      {/* Windows XP Shutdown Screen */}
      <div className="text-center text-white max-w-md mx-auto px-8">
        {/* Your Cyber Logo Area */}
        <div className="mb-8">
          <div className="mb-6">
            <CyberLogoLarge />
          </div>
          <div className="text-xl font-bold tracking-wider text-cyan-400 font-mono">
            HAFSA PORTFOLIO
          </div>
          <div className="text-sm opacity-75 mt-1">
            Cyber Edition • Windows XP Experience
          </div>
        </div>

        {/* Main Message */}
        <div className="mb-8">
          <h1 className="text-xl font-semibold mb-2" style={{ fontFamily: 'Tahoma, sans-serif' }}>
            {content.title}
          </h1>
          {content.subtitle && (
            <p className="text-sm opacity-90" style={{ fontFamily: 'Tahoma, sans-serif' }}>
              {content.subtitle}
            </p>
          )}
        </div>

        {/* Progress Bar */}
        {content.showProgress && (
          <div className="mb-8">
            <div className="w-64 h-4 bg-gray-700 border border-gray-600 rounded mx-auto overflow-hidden">
              <div 
                className="h-full bg-gradient-to-r from-blue-400 to-blue-600 transition-all duration-100 ease-out"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
            <div className="text-xs mt-2 opacity-75">
              {Math.round(progress)}%
            </div>
          </div>
        )}

        {/* Spinner */}
        {content.showSpinner && (
          <div className="mb-8">
            <div className="animate-spin rounded-full h-8 w-8 border-2 border-white border-t-transparent mx-auto"></div>
          </div>
        )}

        {/* Final Stage Additional Info */}
        {shutdownStage === 'final' && (
          <div className="mt-8 space-y-4">
            <div className="bg-black/20 border border-white/20 rounded p-4 text-sm">
              <div className="font-semibold mb-2">Portfolio Session Complete</div>
              <div className="text-xs opacity-75 space-y-1">
                <div>• All portfolio windows have been closed</div>
                <div>• Your browsing session is secure</div>
                <div>• Thank you for visiting Hafsa's Portfolio</div>
              </div>
            </div>
            
            <div className="flex justify-center space-x-4 mt-6">
              <button
                onClick={() => {
                  // Try to close the window/tab
                  if (window.opener) {
                    window.close();
                  } else {
                    // If can't close, try to navigate away
                    window.location.href = 'about:blank';
                  }
                }}
                className="px-6 py-2 bg-gradient-to-b from-gray-300 to-gray-500 text-black text-sm font-medium border border-gray-600 rounded hover:from-gray-200 hover:to-gray-400 transition-all"
                style={{ fontFamily: 'Tahoma, sans-serif' }}
              >
                Close Window
              </button>
              <button
                onClick={() => {
                  // Force page reload
                  window.location.reload(true);
                }}
                className="px-6 py-2 bg-gradient-to-b from-cyan-500 to-cyan-700 text-white text-sm font-medium border border-cyan-800 rounded hover:from-cyan-400 hover:to-cyan-600 transition-all"
                style={{ fontFamily: 'Tahoma, sans-serif' }}
              >
                Restart Portfolio
              </button>
            </div>
          </div>
        )}

        {/* Copyright */}
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-xs opacity-50 font-mono">
          © 2024 Hafsa Moussaid • H◊M Portfolio System • SYS_OFFLINE
        </div>
      </div>

      {/* Subtle background pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="w-full h-full" style={{
          backgroundImage: `radial-gradient(circle at 25% 25%, white 2px, transparent 2px),
                           radial-gradient(circle at 75% 75%, white 2px, transparent 2px)`,
          backgroundSize: '50px 50px'
        }}></div>
      </div>
    </div>
  );
};

export default WindowsXPShutdown;
