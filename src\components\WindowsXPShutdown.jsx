import React, { useState, useEffect } from 'react';

const WindowsXPShutdown = () => {
  const [shutdownStage, setShutdownStage] = useState('saving');
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    const stages = [
      { stage: 'saving', duration: 2000, message: 'Saving your settings...' },
      { stage: 'closing', duration: 1500, message: 'Windows is shutting down...' },
      { stage: 'final', duration: 1000, message: 'It\'s now safe to turn off your computer.' }
    ];

    let currentStageIndex = 0;
    let progressInterval;

    const nextStage = () => {
      if (currentStageIndex < stages.length) {
        const currentStage = stages[currentStageIndex];
        setShutdownStage(currentStage.stage);
        setProgress(0);

        // Animate progress bar
        progressInterval = setInterval(() => {
          setProgress(prev => {
            if (prev >= 100) {
              clearInterval(progressInterval);
              currentStageIndex++;
              setTimeout(nextStage, 500);
              return 100;
            }
            return prev + (100 / (currentStage.duration / 50));
          });
        }, 50);
      }
    };

    nextStage();

    return () => {
      if (progressInterval) clearInterval(progressInterval);
    };
  }, []);

  const getShutdownContent = () => {
    switch (shutdownStage) {
      case 'saving':
        return {
          title: 'Saving your settings...',
          subtitle: 'Please wait while Windows saves your personal settings.',
          showProgress: true,
          showSpinner: true
        };
      case 'closing':
        return {
          title: 'Windows is shutting down...',
          subtitle: 'Please wait while Windows closes all programs.',
          showProgress: true,
          showSpinner: true
        };
      case 'final':
        return {
          title: 'It\'s now safe to turn off your computer.',
          subtitle: 'You can now close this window or turn off your computer.',
          showProgress: false,
          showSpinner: false
        };
      default:
        return {
          title: 'Shutting down...',
          subtitle: '',
          showProgress: true,
          showSpinner: true
        };
    }
  };

  const content = getShutdownContent();

  return (
    <div className="fixed inset-0 z-[9999] bg-gradient-to-b from-blue-800 via-blue-900 to-blue-950 flex items-center justify-center">
      {/* Windows XP Shutdown Screen */}
      <div className="text-center text-white max-w-md mx-auto px-8">
        {/* Windows Logo Area */}
        <div className="mb-8">
          <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-red-500 via-yellow-500 to-green-500 rounded-lg flex items-center justify-center shadow-lg">
            <div className="w-8 h-8 bg-white rounded-sm flex items-center justify-center">
              <div className="w-4 h-4 bg-gradient-to-br from-blue-600 to-blue-800 rounded-sm"></div>
            </div>
          </div>
          <div className="text-2xl font-bold tracking-wider">Windows XP</div>
        </div>

        {/* Main Message */}
        <div className="mb-8">
          <h1 className="text-xl font-semibold mb-2" style={{ fontFamily: 'Tahoma, sans-serif' }}>
            {content.title}
          </h1>
          {content.subtitle && (
            <p className="text-sm opacity-90" style={{ fontFamily: 'Tahoma, sans-serif' }}>
              {content.subtitle}
            </p>
          )}
        </div>

        {/* Progress Bar */}
        {content.showProgress && (
          <div className="mb-8">
            <div className="w-64 h-4 bg-gray-700 border border-gray-600 rounded mx-auto overflow-hidden">
              <div 
                className="h-full bg-gradient-to-r from-blue-400 to-blue-600 transition-all duration-100 ease-out"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
            <div className="text-xs mt-2 opacity-75">
              {Math.round(progress)}%
            </div>
          </div>
        )}

        {/* Spinner */}
        {content.showSpinner && (
          <div className="mb-8">
            <div className="animate-spin rounded-full h-8 w-8 border-2 border-white border-t-transparent mx-auto"></div>
          </div>
        )}

        {/* Final Stage Additional Info */}
        {shutdownStage === 'final' && (
          <div className="mt-8 space-y-4">
            <div className="bg-black/20 border border-white/20 rounded p-4 text-sm">
              <div className="font-semibold mb-2">Portfolio Session Complete</div>
              <div className="text-xs opacity-75 space-y-1">
                <div>• All portfolio windows have been closed</div>
                <div>• Your browsing session is secure</div>
                <div>• Thank you for visiting Hafsa's Portfolio</div>
              </div>
            </div>
            
            <div className="flex justify-center space-x-4 mt-6">
              <button 
                onClick={() => window.close()}
                className="px-6 py-2 bg-gradient-to-b from-gray-300 to-gray-500 text-black text-sm font-medium border border-gray-600 rounded hover:from-gray-200 hover:to-gray-400 transition-all"
                style={{ fontFamily: 'Tahoma, sans-serif' }}
              >
                Close Window
              </button>
              <button 
                onClick={() => window.location.reload()}
                className="px-6 py-2 bg-gradient-to-b from-blue-500 to-blue-700 text-white text-sm font-medium border border-blue-800 rounded hover:from-blue-400 hover:to-blue-600 transition-all"
                style={{ fontFamily: 'Tahoma, sans-serif' }}
              >
                Restart Portfolio
              </button>
            </div>
          </div>
        )}

        {/* Copyright */}
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-xs opacity-50">
          © 2024 Hafsa Moussaid Portfolio • Windows XP Experience
        </div>
      </div>

      {/* Subtle background pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="w-full h-full" style={{
          backgroundImage: `radial-gradient(circle at 25% 25%, white 2px, transparent 2px),
                           radial-gradient(circle at 75% 75%, white 2px, transparent 2px)`,
          backgroundSize: '50px 50px'
        }}></div>
      </div>
    </div>
  );
};

export default WindowsXPShutdown;
