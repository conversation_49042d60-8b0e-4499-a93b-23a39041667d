@import "tailwindcss";

/* Windows XP Styles */
.xp-desktop {
  background-image: url('./assets/Images/Windows XP ❤ 4K HD Desktop Wallpaper for 4K Ultra HD TV • Wide.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.xp-window {
  background: #ece9d8;
  border: 2px outset #ece9d8;
  box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.xp-titlebar {
  background: linear-gradient(to bottom, #0997ff 0%, #0053ee 4%, #0050ee 96%, #06f 100%);
  border-bottom: 1px solid #0040d0;
  height: 30px;
  display: flex;
  align-items: center;
  padding: 0 4px;
}

.xp-titlebar.inactive {
  background: linear-gradient(to bottom, #7d7d7d 0%, #5a5a5a 4%, #5a5a5a 96%, #4a4a4a 100%);
  border-bottom: 1px solid #404040;
}

.xp-titlebar-text {
  color: white;
  font-family: 'Tahoma', sans-serif;
  font-size: 11px;
  font-weight: bold;
  margin-left: 4px;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.xp-button {
  background: linear-gradient(to bottom, #f0f0f0 0%, #e0e0e0 50%, #d0d0d0 51%, #c0c0c0 100%);
  border: 1px outset #d4d0c8;
  font-family: 'Tahoma', sans-serif;
  font-size: 11px;
  padding: 2px 8px;
  cursor: pointer;
}

.xp-button:hover {
  background: linear-gradient(to bottom, #f8f8f8 0%, #e8e8e8 50%, #d8d8d8 51%, #c8c8c8 100%);
}

.xp-button:active {
  border: 1px inset #d4d0c8;
  background: linear-gradient(to bottom, #c0c0c0 0%, #d0d0d0 50%, #e0e0e0 51%, #f0f0f0 100%);
}

.xp-window-button {
  width: 21px;
  height: 21px;
  background: linear-gradient(to bottom, #f0f0f0 0%, #e0e0e0 50%, #d0d0d0 51%, #c0c0c0 100%);
  border: 1px outset #d4d0c8;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: bold;
  cursor: pointer;
  margin-left: 2px;
}

.xp-window-button:hover {
  background: linear-gradient(to bottom, #f8f8f8 0%, #e8e8e8 50%, #d8d8d8 51%, #c8c8c8 100%);
}

.xp-window-button:active {
  border: 1px inset #d4d0c8;
  background: linear-gradient(to bottom, #c0c0c0 0%, #d0d0d0 50%, #e0e0e0 51%, #f0f0f0 100%);
}

.xp-taskbar {
  background: linear-gradient(to bottom, #245edb 0%, #1941a5 3%, #1941a5 97%, #14368a 100%);
  border-top: 1px solid #4985e7;
  height: 40px;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.xp-start-button {
  background: linear-gradient(to bottom, #2d8c2d 0%, #1e5f1e 50%, #1a5a1a 51%, #0f4f0f 100%);
  border: 1px outset #4a9f4a;
  color: white;
  font-family: 'Tahoma', sans-serif;
  font-size: 11px;
  font-weight: bold;
  height: 30px;
  padding: 0 20px;
  margin: 5px;
  cursor: pointer;
  display: flex;
  align-items: center;
  border-radius: 3px;
}

.xp-start-button:hover {
  background: linear-gradient(to bottom, #359535 0%, #256f25 50%, #216a21 51%, #165916 100%);
}

.xp-start-button:active {
  border: 1px inset #4a9f4a;
  background: linear-gradient(to bottom, #0f4f0f 0%, #1a5a1a 50%, #1e5f1e 51%, #2d8c2d 100%);
}

.xp-desktop-icon {
  width: 64px;
  height: 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 4px;
  border-radius: 2px;
  transition: background-color 0.1s;
}

.xp-desktop-icon:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.xp-desktop-icon.selected {
  background-color: rgba(0, 120, 215, 0.3);
  border: 1px dotted rgba(0, 120, 215, 0.8);
}

.xp-desktop-icon-image {
  width: 32px;
  height: 32px;
  margin-bottom: 4px;
}

.xp-desktop-icon-text {
  color: white;
  font-family: 'Tahoma', sans-serif;
  font-size: 11px;
  text-align: center;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
  line-height: 1.2;
  word-wrap: break-word;
  max-width: 60px;
}

.xp-system-tray {
  background: linear-gradient(to bottom, #3a7bd6 0%, #2c5aa0 50%, #2c5aa0 51%, #1e3f73 100%);
  border: 1px inset #4985e7;
  height: 26px;
  margin: 7px 4px;
  padding: 0 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-family: 'Tahoma', sans-serif;
  font-size: 11px;
}

/* Matrix rain animation for cyber theme */
@keyframes matrix-fall {
  to {
    transform: translateY(100vh);
    opacity: 0;
  }
}

.matrix-char {
  position: absolute;
  color: #00ff00;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  animation: matrix-fall 3s linear infinite;
  pointer-events: none;
}

/* Draggable window styles */
.dragging {
  user-select: none;
  z-index: 999;
}

.window-content {
  padding: 0;
  overflow: auto;
  flex: 1;
}

/* Start Menu Styles - Bigger and More Realistic */
.xp-start-menu {
  position: fixed;
  bottom: 40px;
  left: 0;
  width: 400px;
  height: 500px;
  background: linear-gradient(to right, #245edb 0%, #245edb 80px, #ece9d8 80px, #ece9d8 100%);
  border: 2px outset #ece9d8;
  box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.4);
  z-index: 1001;
  display: flex;
  flex-direction: column;
}

.xp-start-menu-header {
  height: 80px;
  background: linear-gradient(135deg, #245edb 0%, #1941a5 100%);
  display: flex;
  align-items: flex-end;
  padding: 12px 16px;
  color: white;
  font-family: 'Tahoma', sans-serif;
  font-size: 16px;
  font-weight: bold;
  flex-shrink: 0;
}

.xp-start-menu-items {
  padding: 8px 0;
  flex: 1;
  overflow-y: auto;
}

.xp-start-menu-item {
  display: flex;
  align-items: center;
  padding: 8px 16px 8px 90px;
  font-family: 'Tahoma', sans-serif;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.1s;
  min-height: 40px;
}

.xp-start-menu-item:hover {
  background-color: #316ac5;
  color: white;
}

.xp-start-menu-separator {
  height: 1px;
  background: #c0c0c0;
  margin: 4px 16px;
}

.xp-start-menu-footer {
  height: 50px;
  background: #ece9d8;
  border-top: 1px solid #c0c0c0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  flex-shrink: 0;
}

.xp-start-menu-footer-button {
  background: linear-gradient(to bottom, #f0f0f0 0%, #e0e0e0 50%, #d0d0d0 51%, #c0c0c0 100%);
  border: 1px outset #d4d0c8;
  font-family: 'Tahoma', sans-serif;
  font-size: 11px;
  padding: 4px 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
}

.xp-start-menu-footer-button:hover {
  background: linear-gradient(to bottom, #f8f8f8 0%, #e8e8e8 50%, #d8d8d8 51%, #c8c8c8 100%);
}