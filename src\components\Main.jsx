import React, { useState, useEffect } from 'react';
import { Cpu, HardDrive, MemoryStick, Activity, Fan, Zap, Database, Code, Coffee } from 'lucide-react';

const BlinkingDot = ({ active = true, color = "bg-green-400" }) => (
  <div className={`w-2 h-2 rounded-full ${color} ${active ? 'animate-pulse' : ''}`} />
);

const DigitalDisplay = ({ value, unit, label }) => (
  <div className="border border-green-400 bg-black p-2 font-mono text-green-400 text-center min-w-[80px]">
    <div className="text-lg font-bold">{value}</div>
    <div className="text-xs opacity-70">{unit}</div>
    <div className="text-xs opacity-50">{label}</div>
  </div>
);

const StatusIndicator = ({ level, maxLevel = 5, color = "green" }) => {
  const colors = {
    green: "bg-green-400",
    yellow: "bg-yellow-400",
    red: "bg-red-400",
    blue: "bg-blue-400",
    purple: "bg-purple-400"
  };

  return (
    <div className="flex gap-1">
      {Array.from({ length: maxLevel }, (_, i) => (
        <div
          key={i}
          className={`w-3 h-6 border ${
            i < level 
              ? `${colors[color]} opacity-100` 
              : 'bg-gray-800 opacity-30'
          } ${i < level ? 'shadow-lg' : ''}`}
          style={{
            boxShadow: i < level ? `0 0 10px ${color === 'green' ? '#4ade80' : color === 'yellow' ? '#facc15' : color === 'red' ? '#f87171' : color === 'blue' ? '#60a5fa' : '#a855f7'}` : 'none'
          }}
        />
      ))}
    </div>
  );
};

const MatrixRain = ({ active }) => {
  const [drops, setDrops] = useState([]);

  useEffect(() => {
    if (!active) return;
    
    const interval = setInterval(() => {
      setDrops(prev => {
        const newDrops = [...prev];
        if (Math.random() > 0.7) {
          newDrops.push({
            id: Date.now(),
            left: Math.random() * 100,
            char: String.fromCharCode(33 + Math.random() * 93)
          });
        }
        return newDrops.slice(-8);
      });
    }, 200);

    return () => clearInterval(interval);
  }, [active]);

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {drops.map((drop) => (
        <div
          key={drop.id}
          className="absolute text-green-400 opacity-60 animate-pulse font-mono text-xs"
          style={{
            left: `${drop.left}%`,
            top: '10px',
            animation: 'fall 2s linear forwards'
          }}
        >
          {drop.char}
        </div>
      ))}
      <style jsx>{`
        @keyframes fall {
          to {
            transform: translateY(200px);
            opacity: 0;
          }
        }
      `}</style>
    </div>
  );
};

const Main = ({ setActiveSection }) => {
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 1000);
    return () => clearInterval(timer);
  }, []);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-green-400 text-xl font-mono">SYSTEM_STATUS.EXE</h2>
        <div className="font-mono text-green-400 text-sm">
          {currentTime.toLocaleTimeString()}
        </div>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="border border-green-400 bg-black/90 p-4 relative overflow-hidden">
          <MatrixRain active={true} />
          <div className="relative z-10">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <Cpu className="text-green-400" size={20} />
                <h3 className="text-green-400 font-mono">CPU_CORE</h3>
                <BlinkingDot />
              </div>
              <div className="flex gap-2">
                <DigitalDisplay value="X+" unit="HRS" label="LEARN" />
                <DigitalDisplay value="24/7" unit="MODE" label="STUDY" />
              </div>
            </div>
            
            <div className="space-y-3 text-sm font-mono text-green-300">
              <div className="flex justify-between items-center">
                <span>ROLE: Full-stack Developer</span>
                <StatusIndicator level={4} maxLevel={5} color="green" />
              </div>
              <div className="flex justify-between items-center">
                <span>LEARNING_PROGRESS</span>
                <StatusIndicator level={4} maxLevel={5} color="green" />
              </div>
              <div className="text-xs opacity-70">
                STATUS: LEARNING • MODE: DEDICATED • MOTIVATION: HIGH
              </div>
            </div>
          </div>
        </div>


        <div className="border border-yellow-400 bg-black/90 p-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <MemoryStick className="text-yellow-400" size={20} />
              <h3 className="text-yellow-400 font-mono">MEMORY_BANK</h3>
              <BlinkingDot color="bg-yellow-400" />
            </div>
            <div className="grid grid-cols-3 gap-1">
              {['HTML', 'CSS', 'JS'].map((lang, i) => (
                <div key={lang} className="border border-yellow-400 px-2 py-1 text-yellow-400 font-mono text-xs text-center">
                  {lang}
                </div>
              ))}
            </div>
          </div>
          
          <div className="space-y-3 text-sm font-mono text-yellow-300">
            <div className="flex justify-between items-center">
              <span>HTML</span>
              <StatusIndicator level={5} maxLevel={5} color="yellow" />
            </div>
            <div className="flex justify-between items-center">
              <span>CSS</span>
              <StatusIndicator level={4} maxLevel={5} color="yellow" />
            </div>
            <div className="flex justify-between items-center">
              <span>JAVASCRIPT</span>
              <StatusIndicator level={4} maxLevel={5} color="yellow" />
            </div>
            <div className="text-xs opacity-70 text-center border-t border-yellow-400/30 pt-2">
              FRAMEWORKS: REACT • STYLING: SASS+BOOTSTRAP • RESPONSIVE: TRUE
            </div>
          </div>
        </div>


        <div className="border border-blue-400 bg-black/90 p-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <HardDrive className="text-blue-400" size={20} />
              <h3 className="text-blue-400 font-mono">STORAGE_DRIVE</h3>
              <BlinkingDot color="bg-blue-400" />
            </div>
            <div className="flex gap-2">
              <DigitalDisplay value="4+" unit="PROJ" label="DONE"/>
              <DigitalDisplay value="3+" unit="REPO" label="LIVE" />
            </div>
          </div>
          
          <div className="space-y-3 text-sm font-mono text-blue-300">
            <div className="flex justify-between">
              <span>PROJECT_COMPLETION</span>
              <div className="flex gap-1">
                {Array.from({length: 7}, (_, i) => (
                  <div key={i} className="w-2 h-2 bg-blue-400 rounded-full animate-pulse" 
                       style={{animationDelay: `${i * 0.2}s`}} />
                ))}
              </div>
            </div>
            <div className="flex justify-between">
              <span>REPOSITORY_STATUS</span>
              <div className="text-blue-400 font-bold">ACTIVE</div>
            </div>
            <div className="text-xs opacity-70 text-center border-t border-blue-400/30 pt-2">
              CAPACITY: AVAILABLE • SEEKING: NEW_CHALLENGES
            </div>
          </div>
        </div>

        <div className="border border-purple-400 bg-black/90 p-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <Activity className="text-purple-400" size={20} />
              <h3 className="text-purple-400 font-mono">HEALTH_MON</h3>
              <BlinkingDot color="bg-purple-400" />
            </div>
            <div className="flex items-center gap-2">
              <Coffee className="text-purple-400" size={16} />
              <Fan className="text-purple-400 animate-spin" size={20} />
              <Zap className="text-purple-400" size={16} />
            </div>
          </div>
          
          <div className="space-y-3 text-sm font-mono text-purple-300">
            <div className="flex justify-between items-center">
              <span>WORK_LIFE_BALANCE</span>
              <StatusIndicator level={5} maxLevel={5} color="purple" />
            </div>
            <div className="flex justify-between items-center">
              <span>CAFFEINE_LEVELS</span>
              <StatusIndicator level={4} maxLevel={5} color="purple" />
            </div>
            <div className="flex justify-between items-center">
              <span>DEBUG_EFFICIENCY</span>
              <StatusIndicator level={5} maxLevel={5} color="purple" />
            </div>
            <div className="text-xs opacity-70 text-center border-t border-purple-400/30 pt-2">
              STATUS: OPTIMAL • UPTIME: 99.9% • ERRORS: MINIMAL
            </div>
          </div>
        </div>
      </div>

      <div className="border border-green-400 bg-black/90 p-4">
        <div className="text-green-400 font-mono mb-4 text-center">
          <span className="animate-pulse">█</span> SYSTEM_READY_FOR_INPUT <span className="animate-pulse">█</span>
        </div>
        <div className="flex justify-center gap-4">
          {[
            { name: 'About', color: 'border-green-400 text-green-400 hover:bg-green-400/10', path: './src/components/About' },
            { name: 'Skills', color: 'border-yellow-400 text-yellow-400 hover:bg-yellow-400/10', path: './src/components/Skills' },
            { name: 'Projects', color: 'border-blue-400 text-blue-400 hover:bg-blue-400/10', path: './src/components/Projects' },
            { name: 'Contact', color: 'border-purple-400 text-purple-400 hover:bg-purple-400/10', path: './src/components/Contact' }
          ].map((item) => (
            <button
              key={item.name}
              onClick={() => {
                if (setActiveSection) {
                  setActiveSection(item.name);
                }
                console.log(`Navigating to: ${item.path}`);
              }}
              className={`border-2 px-4 py-2 font-mono transition-all duration-300 transform hover:scale-105 ${item.color}`}
            >
              &gt; {item.name.toUpperCase()}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Main;