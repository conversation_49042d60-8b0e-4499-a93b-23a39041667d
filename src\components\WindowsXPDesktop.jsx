import React, { useState, useEffect } from 'react';
import { Settings } from 'lucide-react';
import { UserIcon, SkillsIcon, CodeIcon, DocumentIcon, MailIcon, ComputerIcon, ProjectsIcon } from './XPIcons';

// Cyber Logo Component for Taskbar
const CyberLogoSmall = () => (
  <div className="relative flex items-center">
    <div className="relative">
      <div className="text-sm font-bold text-cyan-400 font-mono tracking-wider relative z-10">
        <span className="relative">
          H
          <div className="absolute -top-0.5 -right-0.5 w-1 h-1 bg-cyan-400 animate-pulse"></div>
        </span>
        <span className="text-blue-400 mx-0.5">◊</span>
        <span className="relative">
          M
          <div className="absolute -bottom-0.5 -left-0.5 w-1 h-1 bg-blue-400 animate-pulse" style={{ animationDelay: '0.5s' }}></div>
        </span>
      </div>
    </div>
  </div>
);

const WindowsXPDesktop = ({ onOpenWindow, openWindows = [], activeWindow, onFocusWindow }) => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [showStartMenu, setShowStartMenu] = useState(false);
  const [selectedIcon, setSelectedIcon] = useState(null);

  const desktopIcons = [
    {
      id: 'about',
      name: 'About Me',
      icon: <UserIcon size={32} />,
      position: { top: 20, left: 20 }
    },
    {
      id: 'skills',
      name: 'Skills',
      icon: <SkillsIcon size={32} />,
      position: { top: 120, left: 20 }
    },
    {
      id: 'projects',
      name: 'Projects',
      icon: <ProjectsIcon size={32} />,
      position: { top: 220, left: 20 }
    },
    {
      id: 'resume',
      name: 'Resume',
      icon: <DocumentIcon size={32} />,
      position: { top: 320, left: 20 }
    },
    {
      id: 'contact',
      name: 'Contact',
      icon: <MailIcon size={32} />,
      position: { top: 420, left: 20 }
    },
    {
      id: 'portfolio',
      name: 'My Computer',
      icon: <ComputerIcon size={32} />,
      position: { top: 20, left: 120 }
    }
  ];

  const startMenuItems = [
    { id: 'about', name: 'About Me', icon: <UserIcon size={16} /> },
    { id: 'skills', name: 'Skills', icon: <SkillsIcon size={16} /> },
    { id: 'projects', name: 'Projects', icon: <ProjectsIcon size={16} /> },
    { id: 'resume', name: 'Resume', icon: <DocumentIcon size={16} /> },
    { id: 'contact', name: 'Contact', icon: <MailIcon size={16} /> },
    { type: 'separator' },
    { id: 'settings', name: 'Settings', icon: <Settings size={16} /> },
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  const handleIconClick = (iconId) => {
    if (selectedIcon === iconId) {
      // Double click - open window
      onOpenWindow(iconId);
      setSelectedIcon(null);
    } else {
      // Single click - select icon
      setSelectedIcon(iconId);
    }
  };

  const handleDesktopClick = (e) => {
    if (e.target === e.currentTarget) {
      setSelectedIcon(null);
      setShowStartMenu(false);
    }
  };

  const handleStartMenuClick = (itemId) => {
    if (itemId && itemId !== 'settings') {
      onOpenWindow(itemId);
    }
    setShowStartMenu(false);
  };

  const getWindowTitle = (windowId) => {
    const titles = {
      about: 'About Me - Portfolio',
      skills: 'Skills - Portfolio',
      projects: 'Projects - Portfolio',
      resume: 'Resume - Portfolio',
      contact: 'Contact - Portfolio',
      portfolio: 'My Computer - Portfolio'
    };
    return titles[windowId] || 'Unknown Window';
  };

  return (
    <div className="xp-desktop min-h-screen relative overflow-hidden" onClick={handleDesktopClick}>
      {/* Desktop Icons */}
      {desktopIcons.map((icon) => (
        <div
          key={icon.id}
          className={`xp-desktop-icon absolute ${selectedIcon === icon.id ? 'selected' : ''}`}
          style={{ top: icon.position.top, left: icon.position.left }}
          onClick={(e) => {
            e.stopPropagation();
            handleIconClick(icon.id);
          }}
        >
          <div className="xp-desktop-icon-image flex items-center justify-center">
            {icon.icon}
          </div>
          <div className="xp-desktop-icon-text">
            {icon.name}
          </div>
        </div>
      ))}

      {/* Matrix Rain Effect for Cyber Theme */}
      <div className="absolute inset-0 pointer-events-none">
        {Array.from({ length: 20 }, (_, i) => (
          <div
            key={i}
            className="matrix-char"
            style={{
              left: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${3 + Math.random() * 2}s`
            }}
          >
            {String.fromCharCode(33 + Math.random() * 93)}
          </div>
        ))}
      </div>

      {/* Taskbar */}
      <div className="xp-taskbar flex items-center">
        {/* Start Button with Your Cyber Logo */}
        <button
          className="xp-start-button"
          onClick={() => setShowStartMenu(!showStartMenu)}
        >
          <CyberLogoSmall />
          <span className="ml-2 text-white">start</span>
        </button>

        {/* Taskbar Buttons for Open Windows */}
        <div className="flex-1 flex items-center ml-2 space-x-1">
          {openWindows.map((window) => {
            const icon = desktopIcons.find(icon => icon.id === window.id);
            const isActive = activeWindow === window.id;

            return (
              <button
                key={window.id}
                onClick={() => onFocusWindow && onFocusWindow(window.id)}
                className={`
                  h-8 px-3 flex items-center space-x-2 text-xs font-medium transition-all duration-200
                  ${isActive
                    ? 'bg-gradient-to-b from-white via-gray-100 to-gray-200 border border-gray-400 shadow-inner text-gray-800'
                    : 'bg-gradient-to-b from-gray-300 via-gray-400 to-gray-500 hover:from-gray-200 hover:via-gray-300 hover:to-gray-400 border border-gray-600 shadow-sm text-white'
                  }
                  rounded-sm min-w-[120px] max-w-[200px]
                `}
                title={getWindowTitle(window.id)}
              >
                <div className="flex-shrink-0">
                  {React.cloneElement(icon?.icon || <div />, { size: 16 })}
                </div>
                <span className="truncate">
                  {getWindowTitle(window.id).split(' - ')[0]}
                </span>
              </button>
            );
          })}
        </div>

        {/* System Tray */}
        <div className="xp-system-tray">
          <div className="text-center">
            <div>{currentTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</div>
            <div className="text-xs">{currentTime.toLocaleDateString([], { month: 'short', day: 'numeric' })}</div>
          </div>
        </div>
      </div>

      {/* Start Menu */}
      {showStartMenu && (
        <div className="xp-start-menu">
          <div className="xp-start-menu-header">
            <div className="flex items-center">
              <div className="mr-3">
                <CyberLogoSmall />
              </div>
              <div>
                <div className="font-bold">Hafsa Moussaid</div>
                <div className="text-xs opacity-80">SYS_ONLINE</div>
              </div>
            </div>
          </div>
          <div className="xp-start-menu-items">
            {startMenuItems.map((item, index) => (
              item.type === 'separator' ? (
                <div key={index} className="xp-start-menu-separator" />
              ) : (
                <div
                  key={item.id}
                  className="xp-start-menu-item"
                  onClick={() => handleStartMenuClick(item.id)}
                >
                  <span className="mr-3">{item.icon}</span>
                  {item.name}
                </div>
              )
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default WindowsXPDesktop;
