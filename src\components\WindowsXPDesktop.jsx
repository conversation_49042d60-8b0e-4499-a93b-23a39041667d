import React, { useState, useEffect } from 'react';
import { User, Cpu, Code, FileText, Mail, Monitor, Folder, Settings } from 'lucide-react';

const WindowsXPDesktop = ({ onOpenWindow }) => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [showStartMenu, setShowStartMenu] = useState(false);
  const [selectedIcon, setSelectedIcon] = useState(null);

  const desktopIcons = [
    { 
      id: 'about', 
      name: 'About Me', 
      icon: <User size={32} color="#4a90e2" />, 
      position: { top: 20, left: 20 } 
    },
    { 
      id: 'skills', 
      name: 'Skills', 
      icon: <Cpu size={32} color="#f39c12" />, 
      position: { top: 120, left: 20 } 
    },
    { 
      id: 'projects', 
      name: 'Projects', 
      icon: <Code size={32} color="#e74c3c" />, 
      position: { top: 220, left: 20 } 
    },
    { 
      id: 'resume', 
      name: 'Resume', 
      icon: <FileText size={32} color="#2ecc71" />, 
      position: { top: 320, left: 20 } 
    },
    { 
      id: 'contact', 
      name: 'Contact', 
      icon: <Mail size={32} color="#9b59b6" />, 
      position: { top: 420, left: 20 } 
    },
    { 
      id: 'portfolio', 
      name: 'My Portfolio', 
      icon: <Monitor size={32} color="#34495e" />, 
      position: { top: 20, left: 120 } 
    }
  ];

  const startMenuItems = [
    { id: 'about', name: 'About Me', icon: <User size={16} /> },
    { id: 'skills', name: 'Skills', icon: <Cpu size={16} /> },
    { id: 'projects', name: 'Projects', icon: <Code size={16} /> },
    { id: 'resume', name: 'Resume', icon: <FileText size={16} /> },
    { id: 'contact', name: 'Contact', icon: <Mail size={16} /> },
    { type: 'separator' },
    { id: 'settings', name: 'Settings', icon: <Settings size={16} /> },
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  const handleIconClick = (iconId) => {
    if (selectedIcon === iconId) {
      // Double click - open window
      onOpenWindow(iconId);
      setSelectedIcon(null);
    } else {
      // Single click - select icon
      setSelectedIcon(iconId);
    }
  };

  const handleDesktopClick = (e) => {
    if (e.target === e.currentTarget) {
      setSelectedIcon(null);
      setShowStartMenu(false);
    }
  };

  const handleStartMenuClick = (itemId) => {
    if (itemId && itemId !== 'settings') {
      onOpenWindow(itemId);
    }
    setShowStartMenu(false);
  };

  return (
    <div className="xp-desktop min-h-screen relative overflow-hidden" onClick={handleDesktopClick}>
      {/* Desktop Icons */}
      {desktopIcons.map((icon) => (
        <div
          key={icon.id}
          className={`xp-desktop-icon absolute ${selectedIcon === icon.id ? 'selected' : ''}`}
          style={{ top: icon.position.top, left: icon.position.left }}
          onClick={(e) => {
            e.stopPropagation();
            handleIconClick(icon.id);
          }}
        >
          <div className="xp-desktop-icon-image flex items-center justify-center">
            {icon.icon}
          </div>
          <div className="xp-desktop-icon-text">
            {icon.name}
          </div>
        </div>
      ))}

      {/* Matrix Rain Effect for Cyber Theme */}
      <div className="absolute inset-0 pointer-events-none">
        {Array.from({ length: 20 }, (_, i) => (
          <div
            key={i}
            className="matrix-char"
            style={{
              left: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${3 + Math.random() * 2}s`
            }}
          >
            {String.fromCharCode(33 + Math.random() * 93)}
          </div>
        ))}
      </div>

      {/* Taskbar */}
      <div className="xp-taskbar flex items-center">
        {/* Start Button */}
        <button
          className="xp-start-button"
          onClick={() => setShowStartMenu(!showStartMenu)}
        >
          <div className="w-4 h-4 bg-white rounded-full mr-2 flex items-center justify-center">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          </div>
          start
        </button>

        {/* Taskbar Space */}
        <div className="flex-1"></div>

        {/* System Tray */}
        <div className="xp-system-tray">
          <div className="text-center">
            <div>{currentTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</div>
            <div className="text-xs">{currentTime.toLocaleDateString([], { month: 'short', day: 'numeric' })}</div>
          </div>
        </div>
      </div>

      {/* Start Menu */}
      {showStartMenu && (
        <div className="xp-start-menu">
          <div className="xp-start-menu-header">
            <div>
              <div>Hafsa</div>
              <div className="text-xs opacity-80">Portfolio</div>
            </div>
          </div>
          <div className="xp-start-menu-items">
            {startMenuItems.map((item, index) => (
              item.type === 'separator' ? (
                <div key={index} className="xp-start-menu-separator" />
              ) : (
                <div
                  key={item.id}
                  className="xp-start-menu-item"
                  onClick={() => handleStartMenuClick(item.id)}
                >
                  <span className="mr-3">{item.icon}</span>
                  {item.name}
                </div>
              )
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default WindowsXPDesktop;
