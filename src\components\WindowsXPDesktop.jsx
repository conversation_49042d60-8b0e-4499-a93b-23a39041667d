import React, { useState, useEffect } from 'react';
import { Settings } from 'lucide-react';
import { UserIcon, SkillsIcon, CodeIcon, DocumentIcon, MailIcon, ComputerIcon, ProjectsIcon } from './XPIcons';

// Cyber Logo Component for Taskbar
const CyberLogoSmall = () => (
  <div className="relative flex items-center">
    <div className="relative">
      <div className="text-sm font-bold text-cyan-400 font-mono tracking-wider relative z-10">
        <span className="relative">
          H
          <div className="absolute -top-0.5 -right-0.5 w-1 h-1 bg-cyan-400 animate-pulse"></div>
        </span>
        <span className="text-blue-400 mx-0.5">◊</span>
        <span className="relative">
          M
          <div className="absolute -bottom-0.5 -left-0.5 w-1 h-1 bg-blue-400 animate-pulse" style={{ animationDelay: '0.5s' }}></div>
        </span>
      </div>
    </div>
  </div>
);

const WindowsXPDesktop = ({ onOpenWindow, openWindows = [], activeWindow, onFocusWindow, currentWallpaper, onLogOff, onShutdown }) => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [showStartMenu, setShowStartMenu] = useState(false);
  const [selectedIcon, setSelectedIcon] = useState(null);
  const [iconPositions, setIconPositions] = useState({});
  const [draggedIcon, setDraggedIcon] = useState(null);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });

  const desktopIcons = [
    {
      id: 'about',
      name: 'About Me',
      icon: <UserIcon size={32} />,
      position: { top: 20, left: 20 }
    },
    {
      id: 'skills',
      name: 'Skills',
      icon: <SkillsIcon size={32} />,
      position: { top: 120, left: 20 }
    },
    {
      id: 'projects',
      name: 'Projects',
      icon: <ProjectsIcon size={32} />,
      position: { top: 220, left: 20 }
    },
    {
      id: 'resume',
      name: 'Resume',
      icon: <DocumentIcon size={32} />,
      position: { top: 320, left: 20 }
    },
    {
      id: 'contact',
      name: 'Contact',
      icon: <MailIcon size={32} />,
      position: { top: 420, left: 20 }
    },
    {
      id: 'portfolio',
      name: 'My Computer',
      icon: <ComputerIcon size={32} />,
      position: { top: 20, left: 120 }
    }
  ];

  const startMenuItems = [
    { id: 'about', name: 'About Me', icon: <UserIcon size={20} />, description: 'Learn about my background' },
    { id: 'skills', name: 'Skills & Technologies', icon: <SkillsIcon size={20} />, description: 'Technical expertise' },
    { id: 'projects', name: 'My Projects', icon: <ProjectsIcon size={20} />, description: 'Portfolio showcase' },
    { id: 'resume', name: 'Resume', icon: <DocumentIcon size={20} />, description: 'Professional experience' },
    { id: 'contact', name: 'Contact Me', icon: <MailIcon size={20} />, description: 'Get in touch' },
    { type: 'separator' },
    { id: 'portfolio', name: 'My Computer', icon: <ComputerIcon size={20} />, description: 'System overview' },
    { type: 'separator' },
    { id: 'settings', name: 'Control Panel', icon: <Settings size={20} />, description: 'System settings' },
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  const handleIconClick = (iconId) => {
    if (selectedIcon === iconId) {
      // Double click - open window
      onOpenWindow(iconId);
      setSelectedIcon(null);
    } else {
      // Single click - select icon
      setSelectedIcon(iconId);
    }
  };

  const handleDesktopClick = (e) => {
    if (e.target === e.currentTarget) {
      setSelectedIcon(null);
      setShowStartMenu(false);
    }
  };

  const handleIconMouseDown = (e, iconId) => {
    e.preventDefault();
    setDraggedIcon(iconId);
    const rect = e.currentTarget.getBoundingClientRect();
    setDragOffset({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    });
  };

  const handleMouseMove = (e) => {
    if (draggedIcon) {
      const newX = e.clientX - dragOffset.x;
      const newY = e.clientY - dragOffset.y;

      setIconPositions(prev => ({
        ...prev,
        [draggedIcon]: {
          top: Math.max(0, Math.min(newY, window.innerHeight - 100)),
          left: Math.max(0, Math.min(newX, window.innerWidth - 80))
        }
      }));
    }
  };

  const handleMouseUp = () => {
    setDraggedIcon(null);
  };

  useEffect(() => {
    if (draggedIcon) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [draggedIcon, dragOffset]);

  const handleStartMenuClick = (itemId) => {
    if (itemId) {
      onOpenWindow(itemId);
    }
    setShowStartMenu(false);
  };

  const getWindowTitle = (windowId) => {
    const titles = {
      about: 'About Me - Portfolio',
      skills: 'Skills - Portfolio',
      projects: 'Projects - Portfolio',
      resume: 'Resume - Portfolio',
      contact: 'Contact - Portfolio',
      portfolio: 'My Computer - Portfolio'
    };
    return titles[windowId] || 'Unknown Window';
  };

  const getDesktopStyle = () => {
    // If no wallpaper is selected or it's the default XP wallpaper, use CSS class
    if (!currentWallpaper || currentWallpaper.id === 'xp-default') {
      return {};
    }

    // For other wallpapers, override with inline styles
    if (currentWallpaper.path.startsWith('linear-gradient') || currentWallpaper.path.startsWith('#')) {
      return { background: currentWallpaper.path };
    } else {
      return {
        backgroundImage: `url('${currentWallpaper.path}')`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      };
    }
  };

  const getDesktopClassName = () => {
    // Use xp-desktop class for default wallpaper, otherwise use plain class
    if (!currentWallpaper || currentWallpaper.id === 'xp-default') {
      return "xp-desktop min-h-screen relative overflow-hidden";
    }
    return "min-h-screen relative overflow-hidden";
  };

  return (
    <div
      className={getDesktopClassName()}
      style={getDesktopStyle()}
      onClick={handleDesktopClick}
    >
      {/* Desktop Icons */}
      {desktopIcons.map((icon) => {
        const currentPosition = iconPositions[icon.id] || icon.position;
        return (
          <div
            key={icon.id}
            className={`xp-desktop-icon absolute ${selectedIcon === icon.id ? 'selected' : ''} ${draggedIcon === icon.id ? 'cursor-grabbing' : 'cursor-pointer'}`}
            style={{
              top: currentPosition.top,
              left: currentPosition.left,
              userSelect: 'none',
              zIndex: draggedIcon === icon.id ? 1000 : 1
            }}
            onClick={(e) => {
              e.stopPropagation();
              handleIconClick(icon.id);
            }}
            onMouseDown={(e) => handleIconMouseDown(e, icon.id)}
          >
            <div className="xp-desktop-icon-image flex items-center justify-center">
              {icon.icon}
            </div>
            <div className="xp-desktop-icon-text">
              {icon.name}
            </div>
          </div>
        );
      })}

      {/* Matrix Rain Effect for Cyber Theme */}
      <div className="absolute inset-0 pointer-events-none">
        {Array.from({ length: 20 }, (_, i) => (
          <div
            key={i}
            className="matrix-char"
            style={{
              left: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${3 + Math.random() * 2}s`
            }}
          >
            {String.fromCharCode(33 + Math.random() * 93)}
          </div>
        ))}
      </div>

      {/* Taskbar */}
      <div className="xp-taskbar flex items-center">
        {/* Start Button with Your Cyber Logo Only */}
        <button
          className="xp-start-button"
          onClick={() => setShowStartMenu(!showStartMenu)}
          style={{ padding: '0 12px', minWidth: 'auto' }}
        >
          <CyberLogoSmall />
        </button>

        {/* Taskbar Buttons for Open Windows */}
        <div className="flex-1 flex items-center ml-2 space-x-1">
          {openWindows.map((window) => {
            const icon = desktopIcons.find(icon => icon.id === window.id);
            const isActive = activeWindow === window.id;

            return (
              <button
                key={window.id}
                onClick={() => onFocusWindow && onFocusWindow(window.id)}
                className={`
                  h-8 px-3 flex items-center space-x-2 text-xs font-medium transition-all duration-200
                  ${isActive
                    ? 'bg-gradient-to-b from-white via-gray-100 to-gray-200 border border-gray-400 shadow-inner text-gray-800'
                    : 'bg-gradient-to-b from-gray-300 via-gray-400 to-gray-500 hover:from-gray-200 hover:via-gray-300 hover:to-gray-400 border border-gray-600 shadow-sm text-white'
                  }
                  rounded-sm min-w-[120px] max-w-[200px]
                `}
                title={getWindowTitle(window.id)}
              >
                <div className="flex-shrink-0">
                  {React.cloneElement(icon?.icon || <div />, { size: 16 })}
                </div>
                <span className="truncate">
                  {getWindowTitle(window.id).split(' - ')[0]}
                </span>
              </button>
            );
          })}
        </div>

        {/* System Tray */}
        <div className="xp-system-tray">
          <div className="text-center">
            <div>{currentTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</div>
            <div className="text-xs">{currentTime.toLocaleDateString([], { month: 'short', day: 'numeric' })}</div>
          </div>
        </div>
      </div>

      {/* Start Menu - Bigger and More Realistic */}
      {showStartMenu && (
        <div className="xp-start-menu">
          <div className="xp-start-menu-header">
            <div className="flex items-center">
              <div className="mr-4">
                <div className="relative">
                  <div className="text-lg font-bold text-cyan-400 font-mono tracking-wider relative z-10">
                    <span className="relative">
                      H
                      <div className="absolute -top-0.5 -right-0.5 w-1.5 h-1.5 bg-cyan-400 animate-pulse"></div>
                    </span>
                    <span className="text-blue-400 mx-1">◊</span>
                    <span className="relative">
                      M
                      <div className="absolute -bottom-0.5 -left-0.5 w-1.5 h-1.5 bg-blue-400 animate-pulse" style={{ animationDelay: '0.5s' }}></div>
                    </span>
                  </div>
                </div>
              </div>
              <div>
                <div className="font-bold text-lg">Hafsa Moussaid</div>
                <div className="text-sm opacity-90">Portfolio Developer</div>
                <div className="text-xs opacity-70 text-cyan-300">SYS_ONLINE</div>
              </div>
            </div>
          </div>

          <div className="xp-start-menu-items">
            {startMenuItems.map((item, index) => (
              item.type === 'separator' ? (
                <div key={index} className="xp-start-menu-separator" />
              ) : (
                <div
                  key={item.id}
                  className="xp-start-menu-item"
                  onClick={() => handleStartMenuClick(item.id)}
                >
                  <div className="flex items-center w-full">
                    <span className="mr-4 flex-shrink-0">{item.icon}</span>
                    <div className="flex-1">
                      <div className="font-medium">{item.name}</div>
                      {item.description && (
                        <div className="text-xs opacity-70">{item.description}</div>
                      )}
                    </div>
                  </div>
                </div>
              )
            ))}
          </div>

          <div className="xp-start-menu-footer">
            <button
              className="xp-start-menu-footer-button"
              onClick={() => {
                setShowStartMenu(false);
                onLogOff();
              }}
            >
              <Settings size={16} />
              <span>Log Off</span>
            </button>
            <button
              className="xp-start-menu-footer-button"
              onClick={() => {
                setShowStartMenu(false);
                onShutdown();
              }}
            >
              <span>Turn Off Computer</span>
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default WindowsXPDesktop;
